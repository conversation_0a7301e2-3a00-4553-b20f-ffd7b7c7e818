import numpy as np
import time
from typing import Tuple, Dict, List, Any

# Pose2 class remains the same
class Pose2:
    """
    Represents a 2D pose with (x, y) position.
    """
    def __init__(self, x=0.0, y=0.0):
        """
            x (float): X coordinate.
            y (float): Y coordinate.
        """
        self.x = x
        self.y = y

    def as_vector(self):
        """
        Converts the pose to a numpy array [x, y].
        """
        return np.array([self.x, self.y])

    @staticmethod
    def from_vector(vec):
        """
        Creates a Pose2 object from a numpy array.
        """
        return Pose2(vec[0], vec[1])

    def inverse(self):
        """
        Computes the inverse of the pose.
        """
        inv_x = -self.x
        inv_y = -self.y
        return Pose2(inv_x, inv_y)

    def compose(self, other):
        """
        Composes this pose with another pose.
        """
        new_x = self.x + other.x
        new_y = self.y + other.y
        return Pose2(new_x, new_y)

    def between(self, other):
        """
        Computes the relative pose between this pose and another pose.
        """
        return Pose2(other.x - self.x, other.y - self.y)

    def retract(self, delta):
        """
        Applies a delta to the pose to get a new pose.
        """
        updated_pose = Pose2(self.x + delta[0], self.y + delta[1])
        return updated_pose

    def local_coordinates(self, other):
        """
        Computes the local coordinates (delta) between this pose and another pose.
        """
        delta = np.array([other.x - self.x, other.y - self.y])
        return delta

    def __repr__(self):
        return f"({self.x:.4e}, {self.y:.4e})"

#Key
class Key:
    """
    Represents a unique identifier for variables in the factor graph.
    """
    def __init__(self, id):
        self.id = id

    def __hash__(self):
        return hash(self.id)

    def __eq__(self, other):
        return self .id == other.id

    def __repr__(self):
        return f"{self.id}"

# PriorFactor
class PriorFactor:
    """
    Represents a prior on a single pose variable.
    """
    def __init__(self, key, prior_pose, noise_cov):

        self.key = key
        self.prior_pose = prior_pose
        self.noise_cov = noise_cov
        self.noise_cov_inv = np.linalg.inv(noise_cov)

    def error(self, values):
        """
        Computes the error of the factor.
        """
        pose = values.at(self.key)
        error = pose.as_vector() - self.prior_pose.as_vector()
        return error

    def jacobian(self, values):
        """
        Computes the Jacobian of the error function with respect to the variable.
        """
        J = np.eye(2)
        return J

class BetweenFactor:
    """
    Represents an odometry measurement between two poses.
    """
    def __init__(self, key1, key2, relative_pose, noise_cov):
        """
        Initializes the BetweenFactor.
        """
        self.key1 = key1
        self.key2 = key2
        self.relative_pose = relative_pose
        self.noise_cov = noise_cov
        self.noise_cov_inv = np.linalg.inv(noise_cov)

    def error(self, values):
        """
        Computes the error of the factor.
        """
        pose1 = values.at(self.key1)
        pose2 = values.at(self.key2)
        predicted_relative = pose1.between(pose2)
        error = predicted_relative.as_vector() - self.relative_pose.as_vector()
        return error

    def jacobian(self, values):
        """
        Computes the Jacobian of the error function with respect to the variables.
        """
        J1 = -np.eye(2)
        J2 = np.eye(2)
        return np.hstack((J1, J2))

class UnaryFactor:
    """
    Custom unary factor representing a GPS-like measurement (only position).
    """
    def __init__(self, key, x, y, noise_cov):

        self.key = key
        self.mx = x
        self.my = y
        self.noise_cov = noise_cov
        self.noise_cov_inv = np.linalg.inv(noise_cov)

    def error(self, values):

        pose = values.at(self.key)
        error = np.array([pose.x - self.mx, pose.y - self.my])
        return error

    def jacobian(self, values):
    
        J = np.eye(2)
        return J

class NonlinearFactorGraph:
    """
    Represents a nonlinear factor graph, containing all the factors.
    """
    def __init__(self):
        self.factors = []

    def add(self, factor):
        
        self.factors.append(factor)

    def error(self, values):
        """
        Computes the total error for the current estimates.
        """
        total_error = 0.0
        for factor in self.factors:
            e = factor.error(values)
            total_error += e.T @ factor.noise_cov_inv @ e
        return total_error

    def print(self):
        """
        Prints information about the factor graph.
        """
        print(f"NonlinearFactorGraph with {len(self.factors)} factors.") #number of factor

# Enhanced Values class
class Values:
    """
    Holds estimates for the variables in the factor graph.
    """
    def __init__(self):
        self.values: Dict[Any, Any] = {}

    def insert(self, key, value):
        """
        Inserts a new variable estimate.
        """
        if key in self.values:
            raise KeyError(f"Key {key} already exists.")
        self.values[key] = value

    def insert_or_assign(self, key, value):
        """
        Inserts or assigns a variable estimate.
        """
        self.values[key] = value

    def at(self, key):
        """
        Retrieves the estimate for a variable.
        """
        if key not in self.values:
            raise KeyError(f"Key {key} does not exist.")
        return self.values[key]

    def exists(self, key):
        """
        Checks if a variable exists.
        """
        return key in self.values

    def update(self, key, value):
        """
        Updates the estimate for a variable.
        """
        if key not in self.values:
            raise KeyError(f"Key {key} does not exist.")
        self.values[key] = value

    def erase(self, key):
        """
        Erases a variable estimate.
        """
        if key in self.values:
            del self.values[key]
        else:
            raise KeyError(f"Key {key} does not exist.")

    def keys(self):
        """
        Returns all the keys in the Values.
        """
        return list(self.values.keys())

    def dim(self):
        """
        Computes the total dimensionality of all variables.
        """
        return sum(2 for _ in self.values)  # Each Pose2 has 2 dimensions

    def retract(self, delta_vector):
        """
        Retracts the delta vector to update the values.
        """
        new_values = Values()
        idx = 0
        for key in self.keys():
            pose = self.at(key)
            delta = delta_vector[idx:idx+2]
            updated_pose = pose.retract(delta)
            new_values.insert(key, updated_pose)
            idx += 2
        return new_values

    def local_coordinates(self, other):
        """
        Computes the local coordinates (delta vector) between this and another Values.
        """
        delta_list = []
        for key in self.keys():
            pose_self = self.at(key)
            pose_other = other.at(key)
            delta = pose_self.local_coordinates(pose_other)
            delta_list.append(delta)
        return np.concatenate(delta_list)

    def print(self, title="Values:"):
        """
        Prints all variable estimates.
        """
        print(title)
        for key in sorted(self.values.keys(), key=lambda k: k.id):
            value = self.values[key]
            print(f"Value {key}: {value}")

# GivensRotationUtils
class GivensRotationUtils:

    @staticmethod
    def compute_givens_rotation(a: float, b: float) -> Tuple[float, float]:
        """
        Compute the cosine (c) and sine (s) for a Givens rotation.
        """
        r = np.hypot(a, b)
        if r == 0:
            c = 1.0
            s = 0.0
        else:
            c = a / r
            s = -b / r
        return c, s

    @staticmethod
    def apply_givens_rotation(R: np.ndarray, c: float, s: float, k: int, l: int):
        """
        Apply a Givens rotation to rows k and l of matrix R.
        """
        G = np.array([[c, -s], [s, c]])
        R[[k, l], :] = G @ R[[k, l], :]

    @staticmethod
    def qr_decomposition(H: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Perform QR decomposition using Givens rotation.
        """
        m, n = H.shape
        Q = np.eye(m)
        R = H.copy()

        for j in range(n):
            for i in range(j+1, m):
                a = R[j, j]
                b = R[i, j]
                if b != 0:
                    c, s = GivensRotationUtils.compute_givens_rotation(a, b)
                    GivensRotationUtils.apply_givens_rotation(R, c, s, j, i)
                    GivensRotationUtils.apply_givens_rotation(Q, c, s, j, i)
        return Q.T, R

    @staticmethod
    def solve_linear_system(H: np.ndarray, b: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Solve the linear system Hx = b using QR decomposition.
        """
        Q, R = GivensRotationUtils.qr_decomposition(H)
        Q_b = Q.T @ b
        x = np.linalg.solve(R, Q_b)
        return x, R

# GivensQROptimizer
class GivensQROptimizer:
    """
    Optimizes the factor graph using Givens rotation QR decomposition.
    """
    def __init__(self, graph, initial_values):
        self.graph = graph
        self.values = initial_values
        self.error_history = []  
        self.iteration_times = []  
        self.final_H = None  
        self.final_R = None  
        self.key_list = []
        self.key_index = {}

    def optimize(self, max_iterations=100):
        """
        Performs optimization.
        """
        total_time = 0.0  
        self.key_list = self.values.keys()
        self.key_index = {key: i for i, key in enumerate(self.key_list)}

        for iteration in range(max_iterations):
            start_time = time.time() 

            num_vars = len(self.values.values)
            H = np.zeros((num_vars * 2, num_vars * 2))
            b = np.zeros(num_vars * 2)

            for factor in self.graph.factors:
                if isinstance(factor, BetweenFactor):
                    e = factor.error(self.values)
                    J = factor.jacobian(self.values)
                    idx_i = self.key_index[factor.key1] * 2
                    idx_j = self.key_index[factor.key2] * 2

                    # H = J^T Sigma^{-1} J
                    J_i = J[:, :2]
                    J_j = J[:, 2:]
                    H_i = J_i.T @ factor.noise_cov_inv @ J_i
                    H_j = J_j.T @ factor.noise_cov_inv @ J_j
                    H_ij = J_i.T @ factor.noise_cov_inv @ J_j

                    H[idx_i:idx_i+2, idx_i:idx_i+2] += H_i
                    H[idx_j:idx_j+2, idx_j:idx_j+2] += H_j
                    H[idx_i:idx_i+2, idx_j:idx_j+2] += H_ij
                    H[idx_j:idx_j+2, idx_i:idx_i+2] += H_ij.T
                    
                    # b = J^T Sigma^{-1} e
                    b_i = J_i.T @ factor.noise_cov_inv @ e
                    b_j = J_j.T @ factor.noise_cov_inv @ e

                    b[idx_i:idx_i+2] += b_i
                    b[idx_j:idx_j+2] += b_j

                elif isinstance(factor, UnaryFactor):
                    e = factor.error(self.values)
                    J = factor.jacobian(self.values)
                    idx = self.key_index[factor.key] * 2

                    # Update H and b
                    H_i = J.T @ factor.noise_cov_inv @ J
                    b_i = J.T @ factor.noise_cov_inv @ e

                    H[idx:idx+2, idx:idx+2] += H_i
                    b[idx:idx+2] += b_i

                elif isinstance(factor, PriorFactor):
                    e = factor.error(self.values)
                    J = factor.jacobian(self.values)
                    idx = self.key_index[factor.key] * 2

                    # Update H and b
                    H_i = J.T @ factor.noise_cov_inv @ J
                    b_i = J.T @ factor.noise_cov_inv @ e

                    H[idx:idx+2, idx:idx+2] += H_i
                    b[idx:idx+2] += b_i

            # Save H at the current iteration
            self.final_H = H.copy()
            self.final_b = b.copy()

            # Solve for the update using Givens QR decomposition
            try:
                dx, R = GivensRotationUtils.solve_linear_system(H, b)
            except np.linalg.LinAlgError:
                print("Singular matrix encountered in solving linear system.")
                break

            self.final_R = R.copy()

            # Update variables
            for key in self.key_list:
                idx = self.key_index[key] * 2
                delta = -dx[idx:idx+2]
                pose = self.values.at(key)
                updated_pose = pose.retract(delta)
                self.values.update(key, updated_pose)

            # Record total error and iteration time
            total_error = self.graph.error(self.values)
            self.error_history.append(total_error)

            iteration_time = time.time() - start_time
            self.iteration_times.append(iteration_time)
            total_time += iteration_time 

            # Output iteration info
            print(f"Iteration {iteration+1}, Time: {iteration_time:.6f}s")
            self.values.print(f"Iteration {iteration+1} Estimates:")

            # Check convergence
            if np.linalg.norm(dx) < 1e-10:
                print(f"Converged at iteration {iteration+1}\n")
                break

        print(f"Total Iteration Time: {total_time:.6f}s")
        return self.values

# Marginals covariances
class Marginals:
    """
    Computes marginal covariances for variables in the factor graph.
    """
    def __init__(self, optimizer: GivensQROptimizer):
        self.optimizer = optimizer
        self.key_list = optimizer.key_list
        self.key_index = optimizer.key_index
        self.dim = len(self.key_list) * 2

        # Use the final R matrix from optimizer
        R = optimizer.final_R
        if R is None:
            raise ValueError("R matrix is not available.")

        # Compute covariance matrix: Σ = (R^{-1})(R^{-1})^T
        R_inv = np.linalg.inv(R)
        self.covariance = R_inv @ R_inv.T

    def marginal_covariance(self, key):
        """
        Computes the marginal covariance for a given key.
        """
        idx = self.key_index[key] * 2
        cov = self.covariance[idx:idx+2, idx:idx+2]
        return cov

# Main function
def main():
    graph = NonlinearFactorGraph()

    num_poses = 5

    # Define true positions and add odometry factors
    odometry_noise = np.diag([2.0**2, 2.0**2]) 

    keys = [Key(i+1) for i in range(num_poses)]

    # Define true positions
    positions = []
    for i in range(num_poses):
        x = i * 2.0  # Move 2 meters along x-axis each time
        y = i * 1.0
        positions.append((x, y))

    # Add odometry factors between consecutive poses
    for i in range(num_poses - 1):
        delta_x = 2.0
        delta_y = 1.0  
        relative_pose = Pose2(delta_x, delta_y)
        graph.add(BetweenFactor(keys[i], keys[i+1], relative_pose, odometry_noise))

    # Add GPS measurements at every pose with increased noise
    gps_noise = np.diag([0.1**2, 0.1**2])

    for i in range(num_poses):
        x, y = positions[i]
        graph.add(UnaryFactor(keys[i], x, y, gps_noise))

    # Add prior factor at the first pose
    prior_noise = np.diag([0.1**2, 0.1**2])
    graph.add(PriorFactor(keys[0], Pose2(positions[0][0], positions[0][1]), prior_noise))

    # print the factor graph
    graph.print()  

    # Create the data structure to hold the initial estimate to the solution
    initial_estimate = Values()

    # Set initial estimates with larger perturbations
    np.random.seed(42)
    for i in range(num_poses):
        x = i * 2.0 + np.random.randn()
        y = np.random.randn()
        initial_estimate.insert(keys[i], Pose2(x, y))

    initial_estimate.print("\nInitial Estimate:")

    # Optimize using Givens QR decomposition
    optimizer = GivensQROptimizer(graph, initial_estimate)
    result = optimizer.optimize()

    print("\nFinal Result:")
    result.print()

    # Calculate and print marginal covariances for all variables
    marginals = Marginals(optimizer)
    for key in sorted(result.keys(), key=lambda k: k.id):
        cov = marginals.marginal_covariance(key)
        print(f"\nx{key} covariance:")
        for row in cov:
            print(' '.join(f"{elem:.4e}" for elem in row))

if __name__ == "__main__":
    main()