import networkx as nx
import matplotlib.pyplot as plt
import numpy as np

# --- Reusable Plotting Function (Improved Version) ---
def plot_factor_graph(nodes_dict, factors_dict, title, filename, layout_type='trajectory'):
    """
    Generates and saves a clearer visualization of a factor graph.

    Args:
        nodes_dict (dict): Dictionary of nodes, e.g., {'poses': [...], 'landmarks': [...]}.
        factors_dict (dict): Dictionary mapping factor names to connected variables.
        title (str): The title for the plot.
        filename (str): Filename for saving the plot.
        layout_type (str): 'trajectory' for sequential layouts, 'spring' for others.
    """
    G = nx.Graph()
    pos = {}
    
    # --- 1. Add and Position Variable Nodes ---
    for node_type, nodes in nodes_dict.items():
        for i, node_name in enumerate(nodes):
            G.add_node(node_name, type=node_type)
            # Define positions for a clear layout
            if layout_type == 'trajectory':
                if node_type == 'poses' or node_type == 'states':
                    # Layout poses/states in a curve to avoid overlaps
                    angle = i * (np.pi / (len(nodes) / 2.0 + 3))
                    radius = 2.0 + i * 0.4
                    pos[node_name] = (radius * np.cos(angle), radius * np.sin(angle))
                elif node_type == 'landmarks':
                    angle = i * (2 * np.pi / len(nodes))
                    pos[node_name] = (8 * np.cos(angle), 8 * np.sin(angle))
                elif node_type == 'inputs':
                    state_pos = pos.get(f's{i}') or pos.get(f'e{i}')
                    if state_pos:
                        pos[node_name] = (state_pos[0], state_pos[1] - 2.5)
    
    # --- 2. Add Factor Nodes and Edges ---
    for factor_name, connected_vars in factors_dict.items():
        G.add_node(factor_name, type='factor')
        if layout_type == 'trajectory' and connected_vars:
            # Position factors at the centroid of connected variables
            avg_x = sum(pos[cn][0] for cn in connected_vars) / len(connected_vars)
            avg_y = sum(pos[cn][1] for cn in connected_vars) / len(connected_vars)
            pos[factor_name] = (avg_x, avg_y)
        for var_name in connected_vars:
            G.add_edge(factor_name, var_name)

    # Use spring layout if not a trajectory or for complex graphs
    if layout_type == 'spring' or not pos:
        # Increased k value to spread nodes apart
        pos = nx.spring_layout(G, seed=42, k=1.5, iterations=100)

    # --- 3. Style and Draw the Graph ---
    # Increased figure size for better clarity
    plt.figure(figsize=(22, 14))
    
    node_colors, node_sizes, labels = {}, {}, {}
    for node, data in G.nodes(data=True):
        labels[node] = node
        node_type = data.get('type', 'factor')
        node_category = 'variable'
        if node_type == 'poses':
            node_colors[node] = 'skyblue'
            node_sizes[node] = 800
        elif node_type == 'landmarks':
            node_colors[node] = 'lightgreen'
            node_sizes[node] = 700
        elif node_type in ['states', 'inputs']:
            node_colors[node] = 'lightgray'
            node_sizes[node] = 700
        else: # factor
            node_category = 'factor'
            node_colors[node] = 'salmon'
            node_sizes[node] = 180
            labels[node] = ''

    # Draw nodes and edges
    nx.draw_networkx_edges(G, pos, edge_color='gray', alpha=0.6)
    nx.draw_networkx_nodes(G, pos, nodelist=[n for n,d in G.nodes(data=True) if d.get('type') != 'factor'],
                           node_color=[node_colors[n] for n,d in G.nodes(data=True) if d.get('type') != 'factor'],
                           node_size=[node_sizes[n] for n,d in G.nodes(data=True) if d.get('type') != 'factor'])
    nx.draw_networkx_nodes(G, pos, nodelist=[n for n,d in G.nodes(data=True) if d.get('type') == 'factor'],
                           node_color='salmon', node_size=180, node_shape='s')
    nx.draw_networkx_labels(G, pos, labels, font_size=9)
    
    plt.title(title, fontsize=24, pad=20)
    
    # Create legend
    legend_elements = [
        plt.Line2D([0], [0], marker='o', color='w', label='Pose', markerfacecolor='skyblue', markersize=15),
        plt.Line2D([0], [0], marker='o', color='w', label='Landmark', markerfacecolor='lightgreen', markersize=15),
        plt.Line2D([0], [0], marker='o', color='w', label='State/Input', markerfacecolor='lightgray', markersize=15),
        plt.Line2D([0], [0], marker='s', color='w', label='Factor', markerfacecolor='salmon', markersize=10)
    ]
    plt.legend(handles=legend_elements, loc='upper right', fontsize=14)
    
    plt.tight_layout()
    plt.savefig(filename)
    print(f"Graph with title '{title}' saved to {filename}")
    plt.close()


# --- Data Generation Functions (Identical to previous code) ---

# --- 1. Mobile Robot ---
def gen_robot_localization():
    # Large-scale SLAM: 50 poses, 20 landmarks
    n_poses, n_lms = 50, 20
    nodes = {'poses': [f'x{i}' for i in range(n_poses)], 'landmarks': [f'l{i}' for i in range(n_lms)]}
    factors = {'f_prior': ['x0']}

    # Odometry factors (sequential pose connections)
    for i in range(n_poses - 1):
        factors[f'f_odom{i}'] = [f'x{i}', f'x{i+1}']

    # Measurement factors (pose-landmark observations)
    import random
    random.seed(42)  # Reproducible results
    measurements = []
    for pose_id in range(n_poses):
        # Each pose observes 2-4 landmarks
        num_obs = random.randint(2, 4)
        observed_landmarks = random.sample(range(n_lms), num_obs)
        for lm_id in observed_landmarks:
            measurements.append((pose_id, lm_id))

    # Add loop closure constraints (every 10 poses)
    for i in range(10, n_poses, 10):
        if i + 5 < n_poses:
            # Loop closure: pose i observes same landmarks as pose i-10
            base_pose = max(0, i - 10)
            shared_landmarks = random.sample(range(n_lms), 2)
            for lm_id in shared_landmarks:
                measurements.append((i, lm_id))
                measurements.append((base_pose, lm_id))

    for i, (p, l) in enumerate(measurements):
        factors[f'f_meas{i}'] = [f'x{p}', f'l{l}']

    return nodes, factors, 'trajectory'

def gen_robot_planning():
    # Large-scale path planning: 30 waypoints
    n_states = 30
    nodes = {'states': [f's{i}' for i in range(n_states)]}
    factors = {'f_start': ['s0'], 'f_goal': [f's{n_states-1}']}

    # Smoothness factors (sequential state connections)
    for i in range(n_states - 1):
        factors[f'f_smooth{i}'] = [f's{i}', f's{i+1}']

    # Collision avoidance factors (more complex obstacle field)
    import random
    random.seed(123)
    obstacles = []
    for i in range(1, n_states-1, 3):  # Every 3rd state has potential obstacles
        if i + 2 < n_states:
            obstacles.append((i, i+1))
            obstacles.append((i+1, i+2))

    # Add some non-sequential collision constraints
    for i in range(5, n_states-5, 7):
        if i + 5 < n_states:
            obstacles.append((i, i+5))  # Long-range collision constraints

    for i, (s1, s2) in enumerate(obstacles):
        factors[f'f_collision{i}'] = [f's{s1}', f's{s2}']

    return nodes, factors, 'path'

def gen_robot_control():
    # Large-scale control: 20 time steps for comprehensive testing
    n_steps = 20
    nodes = {'states': [f'e{i}' for i in range(n_steps)], 'inputs': [f'u{i}' for i in range(n_steps-1)]}
    factors = {}

    # Dynamics and cost factors
    for i in range(n_steps - 1):
        factors[f'f_dyn{i}'] = [f'e{i}', f'u{i}', f'e{i+1}']  # Dynamics constraint
        factors[f'f_cost{i}'] = [f'e{i}', f'u{i}']            # Control cost

    # State constraints at specific time steps
    for i in range(0, n_steps, 5):  # Every 5th state
        factors[f'f_state_constraint{i}'] = [f'e{i}']

    # Input smoothness constraints
    for i in range(n_steps - 2):
        factors[f'f_input_smooth{i}'] = [f'u{i}', f'u{i+1}']

    factors[f'f_final_cost'] = [f'e{n_steps-1}']
    return nodes, factors, 'trajectory'

# --- 2. Manipulator ---
def gen_manipulator_localization():
    nodes = {'states': ['JointState']}
    factors = {'f_prior': ['JointState']}
    return nodes, factors, 'spring'

def gen_manipulator_planning():
    # Large-scale manipulator planning: 25 joint configurations
    n_states = 25
    nodes = {'states': [f's{i}' for i in range(n_states)]}
    factors = {'f_start': ['s0'], 'f_goal': [f's{n_states-1}']}

    # Smoothness factors for joint trajectory
    for i in range(n_states - 1):
        factors[f'f_smooth{i}'] = [f's{i}', f's{i+1}']

    # Joint limit constraints
    for i in range(n_states):
        factors[f'f_joint_limits{i}'] = [f's{i}']

    # Collision avoidance in configuration space
    import random
    random.seed(789)
    for i in range(2, n_states-2, 4):  # Every 4th configuration
        if i + 3 < n_states:
            factors[f'f_collision{i}'] = [f's{i}', f's{i+2}']

    # Workspace constraints at key configurations
    for i in range(5, n_states, 8):
        factors[f'f_workspace{i}'] = [f's{i}']

    return nodes, factors, 'trajectory'
    
def gen_manipulator_control():
    return gen_robot_control() # The structure is identical

# --- 3. Autonomous Vehicle ---
def gen_autovehicle_localization():
    # Large-scale autonomous vehicle SLAM: 40 poses and 20 landmarks
    n_poses, n_lms = 40, 20
    nodes = {'poses': [f'x{i}' for i in range(n_poses)], 'landmarks': [f'l{i}' for i in range(n_lms)]}
    factors = {'f_prior': ['x0']}

    # Odometry factors
    for i in range(n_poses - 1):
        factors[f'f_odom{i}'] = [f'x{i}', f'x{i+1}']

    # Dense measurement pattern for autonomous driving
    import random
    random.seed(321)
    measurements = []

    # Each pose observes multiple landmarks (sensor-rich environment)
    for pose_id in range(n_poses):
        # Autonomous vehicles have rich sensor data
        num_obs = random.randint(3, 7)
        observed_landmarks = random.sample(range(n_lms), min(num_obs, n_lms))
        for lm_id in observed_landmarks:
            measurements.append((pose_id, lm_id))

    # Loop closures for urban driving scenarios
    for i in range(15, n_poses, 12):  # Every 12 poses starting from 15
        if i + 8 < n_poses:
            # Revisiting same area - observe same landmarks
            base_pose = max(0, i - 15)
            shared_landmarks = random.sample(range(n_lms), 3)
            for lm_id in shared_landmarks:
                measurements.append((i, lm_id))
                measurements.append((base_pose, lm_id))

    for i, (p, l) in enumerate(measurements):
        factors[f'f_meas{i}'] = [f'x{p}', f'l{l}']

    return nodes, factors, 'trajectory'

def gen_autovehicle_planning():
    n_states = 8
    nodes = {'states': [f's{i}' for i in range(n_states)]}
    factors = {'f_start': ['s0'], 'f_goal': [f's{n_states-1}']}
    for i in range(n_states - 1): factors[f'f_kinematics{i}'] = [f's{i}', f's{i+1}']
    for i in range(n_states): factors[f'f_coll{i}'] = [f's{i}']
    return nodes, factors, 'trajectory'

def gen_autovehicle_control():
    n_steps = 5
    nodes = {'states': [f'e{i}' for i in range(n_steps)], 'inputs': [f'u{i}' for i in range(n_steps-1)]}
    factors = {}
    for i in range(n_steps - 1):
        factors[f'f_dyn{i}'] = [f'e{i}', f'u{i}', f'e{i+1}']
        factors[f'f_kin{i}'] = [f'e{i}', f'u{i}', f'e{i+1}']
        factors[f'f_cost{i}'] = [f'e{i}', f'u{i}']
    factors[f'f_final_cost'] = [f'e{n_steps-1}']
    return nodes, factors, 'trajectory'

# --- 4. Quadrotor ---
def gen_quadrotor_localization(): # VIO
    n_poses, n_lms = 10, 5
    nodes = {'poses': [f'x{i}' for i in range(n_poses)], 'landmarks': [f'l{i}' for i in range(n_lms)]}
    factors = {'f_prior': ['x0']}
    for i in range(n_poses - 1): factors[f'f_imu{i}'] = [f'x{i}', f'x{i+1}'] # IMU factors
    meas = [(0,0), (1,0), (1,1), (2,1), (3,2), (4,2), (5,3), (6,3), (7,4), (8,4), (9,2)] # Loop closure
    for i, (p,l) in enumerate(meas): factors[f'f_cam{i}'] = [f'x{p}', f'l{l}'] # Camera factors
    # Use spring layout for better visualization of complex 3D structure
    return nodes, factors, 'spring'
    
def gen_quadrotor_planning():
    return gen_autovehicle_planning() # Structure is identical

def gen_quadrotor_control():
    return gen_autovehicle_control() # Structure is identical

# --- Main Execution Script ---
if __name__ == '__main__':
    all_graphs_to_generate = {
        "app1_robot_localization.png": ("Mobile Robot - Localization", gen_robot_localization),
        "app1_robot_planning.png": ("Mobile Robot - Planning", gen_robot_planning),
        "app1_robot_control.png": ("Mobile Robot - Control", gen_robot_control),
        "app2_manipulator_localization.png": ("Manipulator - Localization", gen_manipulator_localization),
        "app2_manipulator_planning.png": ("Manipulator - Planning", gen_manipulator_planning),
        "app2_manipulator_control.png": ("Manipulator - Control", gen_manipulator_control),
        "app3_autovehicle_localization.png": ("Autonomous Vehicle - Localization", gen_autovehicle_localization),
        "app3_autovehicle_planning.png": ("Autonomous Vehicle - Planning", gen_autovehicle_planning),
        "app3_autovehicle_control.png": ("Autonomous Vehicle - Control", gen_autovehicle_control),
        "app4_quadrotor_localization.png": ("Quadrotor - Localization (VIO)", gen_quadrotor_localization),
        "app4_quadrotor_planning.png": ("Quadrotor - Planning", gen_quadrotor_planning),
        "app4_quadrotor_control.png": ("Quadrotor - Control", gen_quadrotor_control),
    }

    for filename, (title, data_func) in all_graphs_to_generate.items():
        nodes, factors, layout = data_func()
        plot_factor_graph(nodes, factors, title, filename, layout_type=layout)

    print("\nGeneration of 12 factor graphs complete.")