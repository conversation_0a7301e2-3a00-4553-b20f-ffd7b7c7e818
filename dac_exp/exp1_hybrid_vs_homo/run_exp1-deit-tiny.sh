#!/bin/bash
#SBATCH -o hpc_log/job.%j.out
#SBATCH --partition=i64m512u
#SBATCH -J pytorch
#SBATCH -N 1
#SBATCH --ntasks-per-node=2

# i64m512u
# i96m3tu

source /hpc2ssd/softwares/anaconda3/bin/activate base
source setup_env.sh

hybrid_cfg=dac_exp/exp1_hybrid_vs_homo/config-hybrid-deit-tiny.yaml
homodcim_cfg=dac_exp/exp1_hybrid_vs_homo/config-homoDCIM-deit-tiny.yaml
modify_model_param=dac_exp/utils/modify_model_param.py

######## deit tiny
python3 $modify_model_param $hybrid_cfg embedding_dimension 192
python3 $modify_model_param $hybrid_cfg mlp_size 768
python3 $modify_model_param $hybrid_cfg heads 3

python3 $modify_model_param $homodcim_cfg embedding_dimension 192
python3 $modify_model_param $homodcim_cfg mlp_size 768
python3 $modify_model_param $homodcim_cfg heads 3
echo "------------deit tiny"

# # sl=16
# python3 $modify_model_param $hybrid_cfg sequence_length 16
# python3 $modify_model_param $homodcim_cfg sequence_length 16
# echo "-------sl=16"

# # hybrid
# echo "--hybrid"
# python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp1-hybrid
# # homoDCIM
# echo "--homoDCIM"
# python3 execution_and_estimation.py --config $homodcim_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/homoDCIM-deit-tiny-56DCIM.txt --exp-tag exp1-homoDCIM

# # sl=32
# python3 $modify_model_param $hybrid_cfg sequence_length 32
# python3 $modify_model_param $homodcim_cfg sequence_length 32
# echo "-------sl=32"

# # hybrid
# echo "--hybrid"
# python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp1-hybrid
# # homoDCIM
# echo "--homoDCIM"
# python3 execution_and_estimation.py --config $homodcim_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/homoDCIM-deit-tiny-56DCIM.txt --exp-tag exp1-homoDCIM

# sl=64
python3 $modify_model_param $hybrid_cfg sequence_length 64
python3 $modify_model_param $homodcim_cfg sequence_length 64
echo "-------sl=64"

# # hybrid
# echo "--hybrid"
# python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp1-hybrid
# homoDCIM
echo "--homoDCIM"
python3 execution_and_estimation.py --config $homodcim_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/homoDCIM-deit-tiny-56DCIM.txt --exp-tag exp1-homoDCIM


# sl=128
python3 $modify_model_param $hybrid_cfg sequence_length 128
python3 $modify_model_param $homodcim_cfg sequence_length 128
echo "-------sl=128"

# hybrid
echo "--hybrid"
python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp1-hybrid
# homoDCIM
echo "--homoDCIM"
python3 execution_and_estimation.py --config $homodcim_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/homoDCIM-deit-tiny-56DCIM.txt --exp-tag exp1-homoDCIM

# sl=198
python3 $modify_model_param $hybrid_cfg sequence_length 198
python3 $modify_model_param $homodcim_cfg sequence_length 198
echo "-------sl=198"

# hybrid
echo "--hybrid"
python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp1-hybrid
# homoDCIM
echo "--homoDCIM"
python3 execution_and_estimation.py --config $homodcim_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/homoDCIM-deit-tiny-56DCIM.txt --exp-tag exp1-homoDCIM

echo "all done!!"