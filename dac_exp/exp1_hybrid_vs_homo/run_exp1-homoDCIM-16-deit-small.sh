#!/bin/bash
#SBATCH -o hpc_log/job.%j.out
#SBATCH --partition=i64m512u
#SBATCH -J pytorch
#SBATCH -N 1
#SBATCH --ntasks-per-node=2

# i64m512u
# i96m3tu

source /hpc2ssd/softwares/anaconda3/bin/activate base
source setup_env.sh

homodcim_cfg=dac_exp/exp1_hybrid_vs_homo/config-homoDCIM-16-deit-small.yaml

######## deit tiny
echo "------------deit small"
echo "-------sl=16"
# homoDCIM
echo "--homoDCIM"
python3 execution_and_estimation.py --config $homodcim_cfg --module-layout dac_exp/exp1_hybrid_vs_homo/homoDCIM-deit-small-1block5circle-235DCIM.txt --exp-tag exp1-homoDCIM

echo "all done!!"