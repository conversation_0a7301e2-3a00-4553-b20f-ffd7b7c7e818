import re
import argparse

def modify_yaml_param(file_path, param_name, param_value):
    # 读取 YAML 文件
    with open(file_path, 'r') as f:
        lines = f.readlines()

    # 匹配 `model_param` 部分，并修改对应的键值
    in_model_param = False
    for i, line in enumerate(lines):
        if re.match(r"^\s*model_param:", line):  # 进入 `model_param` 部分
            in_model_param = True
        elif in_model_param and re.match(r"^\s+\w+:", line):  # 检查 `model_param` 下的键
            key = re.match(r"^\s+(\w+):", line).group(1)
            if key == param_name:  # 找到目标键
                indent = re.match(r"^(\s+)", line).group(1)  # 获取缩进
                lines[i] = f"{indent}{param_name}: {param_value}\n"  # 修改值
                break
        elif in_model_param and not re.match(r"^\s+", line):  # 离开 `model_param` 部分
            break

    # 写回文件
    with open(file_path, 'w') as f:
        f.writelines(lines)
    print(f"Updated '{param_name}' to '{param_value}' in 'model_param'.")

if __name__ == "__main__":
    # 设置命令行参数
    parser = argparse.ArgumentParser(description="Modify a parameter in the model_param section of a YAML file.")
    parser.add_argument('file_path', type=str, help="Path to the YAML file")
    parser.add_argument('param_name', type=str, help="The parameter name under model_param to modify")
    parser.add_argument('param_value', help="The new value for the parameter (string, int, or float)")

    args = parser.parse_args()

    # 将参数值转换为适当的数据类型
    try:
        param_value = int(args.param_value)
    except ValueError:
        try:
            param_value = float(args.param_value)
        except ValueError:
            param_value = args.param_value

    # 调用修改函数
    modify_yaml_param(args.file_path, args.param_name, param_value)