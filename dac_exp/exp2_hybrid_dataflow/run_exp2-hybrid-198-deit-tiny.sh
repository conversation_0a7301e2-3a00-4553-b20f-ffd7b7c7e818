#!/bin/bash
#SBATCH -o hpc_log/job.%j.out
#SBATCH --partition=i64m512u
#SBATCH -J pytorch
#SBATCH -N 1
#SBATCH --ntasks-per-node=2

# i64m512u
# i96m3tu

source /hpc2ssd/softwares/anaconda3/bin/activate base
source setup_env.sh

hybrid_cfg=dac_exp/exp2_hybrid_dataflow/config-hybrid-198-deit-tiny.yaml
sb_cfg=dac_exp/exp2_hybrid_dataflow/config-x-former-198-deit-tiny.yaml
rbr_cfg=dac_exp/exp2_hybrid_dataflow/config-isolated-rbr-198-deit-tiny.yaml

######## deit tiny
echo "------------deit tiny"
echo "-------sl=198"

# hybrid dataflow
echo "--hybrid"
python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-hybrid

echo "all done!!"