#!/bin/bash
#SBATCH -o hpc_log/job.%j.out
#SBATCH --partition=i64m512u
#SBATCH -J pytorch
#SBATCH -N 1
#SBATCH --ntasks-per-node=2

source /hpc2ssd/softwares/anaconda3/bin/activate base
source setup_env.sh

hybrid_cfg=dac_exp/exp2_hybrid_dataflow/config-hybrid-deit-tiny.yaml
sb_cfg=dac_exp/exp2_hybrid_dataflow/config-x-former-deit-tiny.yaml
rbr_cfg=dac_exp/exp2_hybrid_dataflow/config-isolated-rbr-deit-tiny.yaml
modify_model_param=dac_exp/utils/modify_model_param.py

######## deit tiny
python3 $modify_model_param $hybrid_cfg embedding_dimension 192
python3 $modify_model_param $hybrid_cfg mlp_size 768
python3 $modify_model_param $hybrid_cfg heads 3

python3 $modify_model_param $sb_cfg embedding_dimension 192
python3 $modify_model_param $sb_cfg mlp_size 768
python3 $modify_model_param $sb_cfg heads 3

python3 $modify_model_param $rbr_cfg embedding_dimension 192
python3 $modify_model_param $rbr_cfg mlp_size 768
python3 $modify_model_param $rbr_cfg heads 3
echo "------------deit tiny"

# # sl=16
# python3 $modify_model_param $hybrid_cfg sequence_length 16
# python3 $modify_model_param $sb_cfg sequence_length 16
# python3 $modify_model_param $rbr_cfg sequence_length 16
# echo "-------sl=16"

# # hybrid dataflow
# echo "--hybrid"
# python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-hybrid
# # sequence blocking dataflow
# echo "--sequence"
# python3 execution_and_estimation.py --config $sb_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-x-former
# # isolated_row-by-row dataflow
# echo "--row-by-row"
# python3 execution_and_estimation.py --config $rbr_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-iso-rbr

# # sl=32
# python3 $modify_model_param $hybrid_cfg sequence_length 32
# python3 $modify_model_param $sb_cfg sequence_length 32
# python3 $modify_model_param $rbr_cfg sequence_length 32
# echo "-------sl=32"

# # hybrid dataflow
# echo "--hybrid"
# python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-hybrid
# # sequence blocking dataflow
# echo "--sequence"
# python3 execution_and_estimation.py --config $sb_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-x-former
# # isolated_row-by-row dataflow
# echo "--row-by-row"
# python3 execution_and_estimation.py --config $rbr_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-iso-rbr

# sl=64
python3 $modify_model_param $hybrid_cfg sequence_length 64
python3 $modify_model_param $sb_cfg sequence_length 64
python3 $modify_model_param $rbr_cfg sequence_length 64
echo "-------sl=64"

# hybrid dataflow
echo "--hybrid"
python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-hybrid
# sequence blocking dataflow
echo "--sequence"
python3 execution_and_estimation.py --config $sb_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-x-former
# isolated_row-by-row dataflow
echo "--row-by-row"
python3 execution_and_estimation.py --config $rbr_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-iso-rbr

# sl=128
python3 $modify_model_param $hybrid_cfg sequence_length 128
python3 $modify_model_param $sb_cfg sequence_length 128
python3 $modify_model_param $rbr_cfg sequence_length 128
echo "-------sl=128"

# hybrid dataflow
echo "--hybrid"
python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-hybrid
# sequence blocking dataflow
echo "--sequence"
python3 execution_and_estimation.py --config $sb_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-x-former
# isolated_row-by-row dataflow
echo "--row-by-row"
python3 execution_and_estimation.py --config $rbr_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-iso-rbr

# sl=198
python3 $modify_model_param $hybrid_cfg sequence_length 198
python3 $modify_model_param $sb_cfg sequence_length 198
python3 $modify_model_param $rbr_cfg sequence_length 198
echo "-------sl=198"

# hybrid dataflow
echo "--hybrid"
python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-hybrid
# sequence blocking dataflow
echo "--sequence"
python3 execution_and_estimation.py --config $sb_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-x-former
# isolated_row-by-row dataflow
echo "--row-by-row"
python3 execution_and_estimation.py --config $rbr_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag exp2-iso-rbr

echo "all done!!"

# i64m512u
# i96m3tu