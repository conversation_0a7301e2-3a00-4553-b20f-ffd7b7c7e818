#!/bin/bash
#SBATCH -o hpc_log/job.%j.out
#SBATCH --partition=i64m512u
#SBATCH -J pytorch
#SBATCH -N 1
#SBATCH --ntasks-per-node=2

# i64m512u
# i96m3tu

source /hpc2ssd/softwares/anaconda3/bin/activate base
source setup_env.sh

sb_cfg=dac_exp/exp2_hybrid_dataflow/config-x-former-198-deit-small.yaml

######## deit tiny
echo "------------deit small"
echo "-------sl=198"

# sequence blocking dataflow
echo "--sequence"
python3 execution_and_estimation.py --config $sb_cfg --module-layout dac_exp/exp2_hybrid_dataflow/hybrid-deit-small-4blocks6circle-588ACIM.txt --exp-tag exp2-x-former

echo "all done!!"