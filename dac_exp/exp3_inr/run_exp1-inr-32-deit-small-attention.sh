#!/bin/bash
#SBATCH -o hpc_log/job.%j.out
#SBATCH --partition=i64m512u
#SBATCH -J pytorch
#SBATCH -N 1
#SBATCH --ntasks-per-node=2

# i64m512u
# i96m3tu

source /hpc2ssd/softwares/anaconda3/bin/activate base
source setup_env.sh

hybrid_cfg=dac_exp/exp3_inr/config-inr-32-deit-small-attention.yaml

######## deit tiny
echo "------------deit small"
echo "-------sl=32"
# hybrid
echo "--hybrid"
python3 execution_and_estimation.py --config $hybrid_cfg --module-layout dac_exp/exp3_inr/hybrid-deit-small-4blocks6circle-588ACIM.txt --exp-tag exp3-inr

echo "all done!!"