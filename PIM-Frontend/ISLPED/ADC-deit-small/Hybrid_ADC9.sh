#!/bin/bash
#SBATCH -o job.%j.out
#SBATCH --partition=debug
#SBATCH -J pytorch
#SBATCH -N 1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --qos=low

source /hpc2ssd/softwares/anaconda3/bin/activate ivit
module load cuda/12.0

python ./inference.py --model deit_small --data ../datasets --print-freq 20 \
    --mapping-type hybrid --ADC-effects --wl-input 8 --wl-weight 8 \
    --onoffratio 10 --cellBit 2 --sub-array-rows 128 --sub-array-cols 128 --parallelRead 128 --ADCprecision 9 \
    --vari 0 --t 0 --v 0 --detect 0 --target 0 \
    --nonlinearityLTP 1.75 --nonlinearityLTD 1.46 --d2dVari 0 --c2cVari 0

# i64m1tga40u
# emergency_gpua40
# emergency_gpu
# i64m1tga800u
# debug