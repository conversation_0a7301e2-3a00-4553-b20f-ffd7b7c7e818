#!/bin/bash
#SBATCH -o job.%j.out
#SBATCH --partition=emergency_gpua40
#SBATCH -J pytorch
#SBATCH -N 1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --qos=low

source /hpc2ssd/softwares/anaconda3/bin/activate ivit
module load cuda/12.0
# conda install pytorch==2.0.0 torchvision==0.15.0 torchaudio==2.0.0 pytorch-cuda=11.8 -c pytorch -c nvidia
# python ./I-ViT/quant_train.py --model deit_tiny --data ./ --epochs 30 --lr 5e-7 --batch-size 32

#2024.09
# python ./inference.py --model vit_base --data ../datasets --resume ./results/checkpoint.pth
# python ./inference.py --model vit_base --data ../datasets --print-freq 20
# python ./inference.py --model vit_base --data ../datasets --print-freq 20 \
#     --nonideal-effects --only-ADC-effects --mapping-type pure_rram --wl-input 8 --wl-weight 8 \
#     --onoffratio 10 --cellBit 2 --subArray 128 --parallelRead 128 --ADCprecision 8 \
#     --vari 0 --t 0 --v 0 --detect 0 --target 0 \
#     --nonlinearityLTP 1.75 --nonlinearityLTD 1.46 --d2dVari 0 --c2cVari 0
python ./inference.py --model deit_small --data ../datasets --print-freq 20 \
    --nonideal-effects --mapping-type pure_rram --wl-input 8 --wl-weight 8 \
    --onoffratio 10 --cellBit 2 --subArray 128 --parallelRead 128 --ADCprecision 8 \
    --vari 0.1 --t 10 --v 0.001 --detect 0 --target 0 \
    --nonlinearityLTP 1.75 --nonlinearityLTD 1.46 --d2dVari 0 --c2cVari 0

# i64m1tga40u
# emergency_gpua40
# emergency_gpu
# i64m1tga800u
# debug