import numpy as np
from pim import wage_quantizer
import torch

def get_partition_size(sub_array_rows, sub_array_cols, bits_per_cell, weight_bits):
    return (int(sub_array_rows), int(sub_array_cols*bits_per_cell//weight_bits))

def split_matrix_multiply(input_matrix, weight_matrix, split_size, split_dim='input_row'):
    """
    Split matrix multiplication into smaller blocks for computation
    
    Args:
        input_matrix: Input matrix of shape (batch_size, input_row, input_col)
        weight_matrix: Weight matrix of shape (weight_row, weight_col)
        split_size: Size of each sub-matrix
        split_dim: Dimension to split along, one of 'input_row', 'weight_row', 'weight_col'
    
    Returns:
        Result matrix with same shape as original matrix multiplication
    """
    if split_dim == 'input_row':
        # Split along input rows
        input_chunks = np.array_split(input_matrix, range(split_size, input_matrix.shape[1], split_size), axis=1)
        # print(f"\n=== Splitting along input rows (split_size={split_size}) ===")
        # print(f"Original input matrix shape: {input_matrix.shape}")
        # print("Original input matrix values:\n", input_matrix)
        # print(f"Weight matrix shape: {weight_matrix.shape}")
        # print("Weight matrix values:\n", weight_matrix)
        # print(f"Number of sub-matrices: {len(input_chunks)}")
        results = []
        for i, chunk in enumerate(input_chunks):
            # print(f"\nSub-matrix {i+1}:")
            # print(f"Input sub-matrix shape: {chunk.shape}")
            # print("Input sub-matrix values:\n", chunk)
            result = chunk @ weight_matrix
            # print(f"Output sub-matrix shape: {result.shape}")
            # print("Output sub-matrix values:\n", result)
            results.append(result)
        final_result = np.concatenate(results, axis=1)
        # print("\nFinal result shape:", final_result.shape)
        # print("Final result values:\n", final_result)
        return final_result
        
    elif split_dim == 'weight_row':
        # Split along weight rows (and input columns)
        weight_chunks = np.array_split(weight_matrix, range(split_size, weight_matrix.shape[0], split_size), axis=0)
        input_chunks = np.array_split(input_matrix, range(split_size, input_matrix.shape[2], split_size), axis=2)
        # print(f"\n=== Splitting along weight rows (split_size={split_size}) ===")
        # print(f"Original input matrix shape: {input_matrix.shape}")
        # print("Original input matrix values:\n", input_matrix)
        # print(f"Weight matrix shape: {weight_matrix.shape}")
        # print("Weight matrix values:\n", weight_matrix)
        # print(f"Number of sub-matrices: {len(weight_chunks)}")
        results = []
        for i, (w_chunk, i_chunk) in enumerate(zip(weight_chunks, input_chunks)):
            # print(f"\nSub-matrix pair {i+1}:")
            # print(f"Input sub-matrix shape: {i_chunk.shape}")
            # print("Input sub-matrix values:\n", i_chunk)
            # print(f"Weight sub-matrix shape: {w_chunk.shape}")
            # print("Weight sub-matrix values:\n", w_chunk)
            result = i_chunk @ w_chunk
            # print(f"Output sub-matrix shape: {result.shape}")
            # print("Output sub-matrix values:\n", result)
            results.append(result)
        final_result = np.sum(np.stack(results), axis=0)
        # print("\nFinal result shape:", final_result.shape)
        # print("Final result values:\n", final_result)
        return final_result
        
    elif split_dim == 'weight_col':
        # Split along weight columns
        weight_chunks = np.array_split(weight_matrix, range(split_size, weight_matrix.shape[1], split_size), axis=1)
        # print(f"\n=== Splitting along weight columns (split_size={split_size}) ===")
        # print(f"Original input matrix shape: {input_matrix.shape}")
        # print("Original input matrix values:\n", input_matrix)
        # print(f"Weight matrix shape: {weight_matrix.shape}")
        # print("Weight matrix values:\n", weight_matrix)
        # print(f"Number of sub-matrices: {len(weight_chunks)}")
        results = []
        for i, chunk in enumerate(weight_chunks):
            # print(f"\nSub-matrix {i+1}:")
            # print(f"Weight sub-matrix shape: {chunk.shape}")
            # print("Weight sub-matrix values:\n", chunk)
            result = input_matrix @ chunk
            # print(f"Output sub-matrix shape: {result.shape}")
            # print("Output sub-matrix values:\n", result)
            results.append(result)
        final_result = np.concatenate(results, axis=2)
        # print("\nFinal result shape:", final_result.shape)
        # print("Final result values:\n", final_result)
        return final_result
        
    else:
        raise ValueError("split_dim must be 'input_row', 'weight_row' or 'weight_col'")

# Test cases
def test_split_matrix_multiply():
    # Test case 1: input_row split
    input1 = np.random.rand(2, 4, 3)  # batch_size=2, input_row=4, input_col=3
    weight1 = np.random.rand(3, 2)    # weight_row=3, weight_col=2
    expected1 = input1 @ weight1
    result1 = split_matrix_multiply(input1, weight1, split_size=2, split_dim='input_row')
    assert np.allclose(result1, expected1)
    
    # Test case 2: weight_row split
    input2 = np.random.rand(2, 3, 4)  # batch_size=2, input_row=3, input_col=4
    weight2 = np.random.rand(4, 2)    # weight_row=4, weight_col=2
    expected2 = input2 @ weight2
    result2 = split_matrix_multiply(input2, weight2, split_size=2, split_dim='weight_row')
    assert np.allclose(result2, expected2)
    
    # Test case 3: weight_col split
    input3 = np.random.rand(2, 3, 4)  # batch_size=2, input_row=3, input_col=4
    weight3 = np.random.rand(4, 6)    # weight_row=4, weight_col=6
    expected3 = input3 @ weight3
    result3 = split_matrix_multiply(input3, weight3, split_size=2, split_dim='weight_col')
    assert np.allclose(result3, expected3)
    
    print("All test cases passed!")

def partition_matrix_multiply_with_ADC(input_matrix, weight_matrix, partition_row_size, partition_col_size, enable_ADC=False, ADCprecision=8):
    """
    Args:
        input_matrix: shape [batch_size, input_row, input_col]
        weight_matrix: shape [weight_row, weight_col]
        partition_row_size: 行分区大小
        partition_col_size: 列分区大小
    Returns:
        result: shape [batch_size, input_row, weight_col]
    """
    batch_size, input_row, input_col = input_matrix.shape
    weight_row, weight_col = weight_matrix.shape
    device = input_matrix.device
    
    # validate dimension matched
    assert input_col == weight_row, f"Matrix dimensions don't match: {input_col} != {weight_row}"
    
    # Create split indices as lists
    weight_row_splits = list(range(partition_row_size, weight_row, partition_row_size))
    weight_col_splits = list(range(partition_col_size, weight_col, partition_col_size))
    
    # Split weight matrix into row chunks
    weight_row_chunks = torch.tensor_split(weight_matrix, weight_row_splits, dim=0)
    
    # Initialize result matrix on GPU
    result = torch.zeros((batch_size, input_row, weight_col), device=device)
    
    # Process each input row separately
    for i in range(input_row):
        input_slice = input_matrix[:, i:i+1, :]
        # print(f"\nProcessing input row {i+1}/{input_row}")
        # print(f"Input slice shape: {input_slice.shape}")
        # print("Input slice values:\n", input_slice)
        
        # Split input slice according to weight row partitions
        input_chunks = torch.tensor_split(input_slice, weight_row_splits, dim=2)
        
        # Initialize temporary result for this row on GPU
        row_result = torch.zeros((batch_size, 1, weight_col), device=device)
        
        # Process each weight row partition
        for j, (w_row_chunk, i_chunk) in enumerate(zip(weight_row_chunks, input_chunks)):
            # print(f"\nWeight row partition {j+1}/{len(weight_row_chunks)}")
            # print(f"Input chunk shape: {i_chunk.shape}")
            # print("Input chunk values:\n", i_chunk)
            # print(f"Weight row chunk shape: {w_row_chunk.shape}")
            # print("Weight row chunk values:\n", w_row_chunk)
            
            # Further split weight chunk into columns
            w_col_chunks = torch.tensor_split(w_row_chunk, weight_col_splits, dim=1)
            
            # Initialize temporary result for this weight row chunk on GPU
            chunk_result = torch.zeros((batch_size, 1, weight_col), device=device)
            
            # Process each weight column partition
            for k, w_col_chunk in enumerate(w_col_chunks):
                # print(f"\nWeight column partition {k+1}/{len(w_col_chunks)}")
                # print(f"Weight column chunk shape: {w_col_chunk.shape}")
                # print("Weight column chunk values:\n", w_col_chunk)
                
                # Compute partial result
                partial_result = i_chunk @ w_col_chunk
                # print(f"Partial result shape: {partial_result.shape}")
                
                if enable_ADC:
                    # print("Partial result shape (before ADC):", partial_result.shape)
                    partial_result = wage_quantizer.LinearQuantizeOut(partial_result, ADCprecision)
                
                # print("Partial result values:\n", partial_result)
                
                # Add to appropriate position in chunk result
                start_col = k * partition_col_size
                end_col = min((k + 1) * partition_col_size, weight_col)
                chunk_result[:, :, start_col:end_col] = partial_result
            
            # Add this chunk's result to row result
            row_result += chunk_result
            # print(f"\nAccumulated row result after partition {j+1}:")
            # print(row_result)
        
        # Copy row result to final result matrix
        result[:, i:i+1, :] = row_result
    
    # print("\nFinal result shape:", result.shape)
    # print("Final result values:\n", result)
    return result

def test_partition_matrix_multiply(): # enable_ADC=False
    print("\n=== Test Case 1: Small matrices with even partitions ===")
    # Test case with small matrices for easy verification
    input_matrix = np.array([
        [  # Batch 1
            [1, 2, 3, 4],  # Row 1
            [5, 6, 7, 8],  # Row 2
        ],
        [  # Batch 2
            [9, 10, 11, 12],  # Row 1
            [13, 14, 15, 16],  # Row 2
        ]
    ], dtype=float)  # Shape: (2, 2, 4)
    
    weight_matrix = np.array([
        [1, 2, 3],
        [4, 5, 6],
        [7, 8, 9],
        [10, 11, 12]
    ], dtype=float)  # Shape: (4, 3)
    
    # Expected result using normal matrix multiplication
    expected = input_matrix @ weight_matrix
    
    # Test with different partition sizes
    partition_row_size = 2
    partition_col_size = 2
    result = partition_matrix_multiply_with_ADC(input_matrix, weight_matrix, 
                                    partition_row_size, partition_col_size, enable_ADC=False)
    
    # Verify results
    assert np.allclose(result, expected)
    print("Expected result:\n", expected)
    print("Actual result:\n", result)
    print("\nTest case 1 passed!")

    print("\n=== Test Case 2: Larger matrices with uneven partitions ===")
    # Test case with larger matrices and uneven partitions
    input_matrix2 = np.array([
        [  # Batch 1
            [1, 2, 3, 4, 5],  # Row 1
            [6, 7, 8, 9, 10],  # Row 2
            [11, 12, 13, 14, 15],  # Row 3
        ],
        [  # Batch 2
            [16, 17, 18, 19, 20],  # Row 1
            [21, 22, 23, 24, 25],  # Row 2
            [26, 27, 28, 29, 30],  # Row 3
        ]
    ], dtype=float)  # Shape: (2, 3, 5)
    
    weight_matrix2 = np.array([
        [1, 2, 3, 4],
        [5, 6, 7, 8],
        [9, 10, 11, 12],
        [13, 14, 15, 16],
        [17, 18, 19, 20]
    ], dtype=float)  # Shape: (5, 4)
    
    expected2 = input_matrix2 @ weight_matrix2
    
    # Test with uneven partition sizes
    partition_row_size = 3  # 5 rows will be split into 3+2
    partition_col_size = 3  # 4 columns will be split into 3+1
    result2 = partition_matrix_multiply_with_ADC(input_matrix2, weight_matrix2,
                                     partition_row_size, partition_col_size, enable_ADC=False)
    
    assert np.allclose(result2, expected2)
    print("Expected result:\n", expected2)
    print("Actual result:\n", result2)
    print("\nTest case 2 passed!")

    print("\n=== Test Case 3: Small matrices with single-element partitions ===")
    # Test case with small matrices and single-element partitions
    input_matrix3 = np.array([
        [  # Batch 1
            [1, 2],  # Row 1
            [3, 4],  # Row 2
        ]
    ], dtype=float)  # Shape: (1, 2, 2)
    
    weight_matrix3 = np.array([
        [5, 6, 7],
        [8, 9, 10]
    ], dtype=float)  # Shape: (2, 3)
    
    expected3 = input_matrix3 @ weight_matrix3
    
    # Test with single-element partitions
    partition_row_size = 1  # Split into individual rows
    partition_col_size = 1  # Split into individual columns
    result3 = partition_matrix_multiply_with_ADC(input_matrix3, weight_matrix3,
                                     partition_row_size, partition_col_size, enable_ADC=False)
    
    assert np.allclose(result3, expected3)
    print("Expected result:\n", expected3)
    print("Actual result:\n", result3)
    print("\nTest case 3 passed!")

def test_attention_matrix_multiply():
    """
    test attention matrix multiply
    verify q @ kt and attn @ v calculation
    """
    import torch
    
    # create small example data
    batch_size = 2
    num_heads = 3
    seq_len = 4
    head_dim = 5
    
    print("\n=== test q @ kt ===")
    # create q and k
    q = torch.randn(batch_size, num_heads, seq_len, head_dim)
    k = torch.randn(batch_size, num_heads, seq_len, head_dim)
    kt = k.transpose(-2, -1)  # [batch_size, num_heads, head_dim, seq_len]
    
    print("input shape:")
    print(f"q shape: {q.shape}")
    print(f"kt shape: {kt.shape}")
    
    # calculate reference result using normal matrix multiplication
    expected_qk = torch.matmul(q, kt)
    print(f"expected output shape: {expected_qk.shape}")
    
    # use partitioned matrix multiplication
    partition_row_size = 2
    partition_col_size = 2
    
    # initialize result tensor
    result_qk = torch.zeros((batch_size, num_heads, seq_len, seq_len), device=q.device)
    
    # process each batch and head
    for b in range(batch_size):
        for h in range(num_heads):
            current_q = q[b, h]    # [seq_len, head_dim]
            current_kt = kt[b, h]  # [head_dim, seq_len]
            current_result = partition_matrix_multiply_with_ADC(
                current_q.unsqueeze(0),
                current_kt,
                partition_row_size,
                partition_col_size
            )
            result_qk[b, h] = current_result.squeeze(0)
    
    print(f"actual output shape: {result_qk.shape}")
    assert torch.allclose(result_qk, expected_qk, rtol=1e-4, atol=1e-4), "q @ kt result not match!"
    
    print("\n=== test attn @ v ===")
    # create attn and v
    attn = torch.softmax(result_qk, dim=-1)  # use the result of the previous step as attention
    v = torch.randn(batch_size, num_heads, seq_len, head_dim)
    
    print("input shape:")
    print(f"attn shape: {attn.shape}")
    print(f"v shape: {v.shape}")
    
    # calculate reference result using normal matrix multiplication
    expected_av = torch.matmul(attn, v)
    print(f"expected output shape: {expected_av.shape}")
    
    # initialize result tensor
    result_av = torch.zeros((batch_size, num_heads, seq_len, head_dim), device=attn.device)
    
    # process each batch and head
    for b in range(batch_size):
        for h in range(num_heads):
            current_attn = attn[b, h]    # [seq_len, seq_len]
            current_v = v[b, h]          # [seq_len, head_dim]
            current_result = partition_matrix_multiply_with_ADC(
                current_attn.unsqueeze(0),  # [1, seq_len, seq_len]
                current_v,                  # [seq_len, head_dim]
                partition_row_size,
                partition_col_size
            )
            result_av[b, h] = current_result.squeeze(0)
    
    print(f"actual output shape: {result_av.shape}")
    assert torch.allclose(result_av, expected_av, rtol=1e-4, atol=1e-4), "attn @ v result not match!"
    
    print("\nall test passed! result fully match")
    print("\nexample result:")
    print("partial results of q @ kt:")
    print(result_qk[0, 0, :2, :2])
    print("\npartial results of attn @ v:")
    print(result_av[0, 0, :2, :2])

if __name__ == "__main__":
    test_attention_matrix_multiply()