import numpy as np
from scipy.integrate import solve_ivp
import matplotlib.pyplot as plt

def solve_lrs(V_values, colors, R_on_initial=6e3, states=['State1','State2','State3']):
    # Params
    R_bot = 19e-9               # 19 nm → m
    r_sat = 19e-9               # 19 nm → m
    alpha = 0.09                # m/s
    c_sat = 2                   
    states_r_init = {
        'State1': 6.4e-9,       # 6.4 nm → m
        'State2': 12e-9,        # 12 nm → m
        'State3': 17.8e-9       # 17.8 nm → m
    }
    rho_ONxL = R_on_initial * np.pi * states_r_init['State3'] * R_bot    # Ω·m2

    def t_ch(V):
        return 6500 * np.exp(-38 * V + 0.7)

    def t_sat(V):
        return 10 ** (-14.7 * V + 6.7)

    def safe_log(x, eps=1e-30):
        return np.log(x) if x > eps else np.log(eps)

    def drdt(t, r, V, r_init):
        if t <= 0:
            return np.array([0.0])
        
        t_stress = t
        term1 = alpha * (r_sat - r_init) / t_stress
        
        log_term = safe_log(t_stress / t_ch(V))
        
        tsat_V = t_sat(V)
        log_denom = safe_log(tsat_V / t_stress)
        
        denominator = 1 + np.exp(c_sat) * log_denom
        
        # Relax the denominator condition (allow positive values close to zero).
        if denominator <= 1e-6:
            return np.array([0.0])
        
        return np.array([term1 * log_term / denominator])

    results = {}
    for state_name, r_init in states_r_init.items():
        results[state_name] = {}
        for V, color in zip(V_values, colors):
            t_eval = np.logspace(-7, 3, 1000)  # Extend to 1e3 seconds.
            sol = solve_ivp(drdt, [t_eval[0], t_eval[-1]], [r_init], 
                            args=(V, r_init), t_eval=t_eval, method='Radau', 
                            rtol=1e-8, atol=1e-20)
            r_t = sol.y[0]
            
            R = rho_ONxL / (np.pi * r_t * R_bot)
            print(f"V={V}, R min={np.min(R):.2e}, R max={np.max(R):.2e}")
            results[state_name][V] = (sol.t, R)
        
    return results

if __name__ == "__main__":
    V_values = [0.2, 0.3, 0.4, 0.5, 0.6, 0.7]
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

    # User input
    R_on_initial = 6e3 

    results = solve_lrs(V_values, colors=colors, R_on_initial=R_on_initial, states=['State1','State2','State3'])
    for state_name in results:
        plt.figure(figsize=(10, 6))
        for (V, (t, R)), color in zip(results[state_name].items(), colors):
            plt.loglog(t, R, color=color, linewidth=2, label=f'V = {V} V')
        
        plt.xlim(1e-7, 1)
        plt.ylim(1e3, 1e7)  
        plt.xlabel('Stress Time (s)', fontsize=12)
        plt.ylabel('Resistance (Ω)', fontsize=12)
        plt.title(f'Resistance vs Stress Time ({state_name})', fontsize=14)
        plt.legend(fontsize=10, loc='best')
        plt.grid(True, which='both', linestyle='--', alpha=0.7)
        plt.savefig(f'Resistance_{state_name}.png', dpi=300, bbox_inches='tight')
        plt.show()