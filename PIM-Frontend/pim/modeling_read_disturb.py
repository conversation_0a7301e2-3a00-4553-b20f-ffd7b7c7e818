import numpy as np
import matplotlib.pyplot as plt
from .modeling_read_disturb_HRS import solve_hrs
from .modeling_read_disturb_LRS import solve_lrs

def resistance_to_state(R, R_min, R_max):
    def calc_threshold(threshold):
        return 1 / (threshold/R_min + (1-threshold)/R_max)
    
    R3_max = calc_threshold(0.75)  
    R2_max = calc_threshold(0.5)   
    R1_max = calc_threshold(0.25)  
    
    state_boundaries = {
        3: (R_min, R3_max),
        2: (R3_max, R2_max),
        1: (R2_max, R1_max),
        0: (R1_max, R_max)
    }
    
    for state in sorted(state_boundaries, reverse=True):
        lower, upper = state_boundaries[state]
        print(f"State {state}: {lower:.2e}Ω < R ≤ {upper:.2e}Ω")
    
    R_clipped = np.clip(R, R_min, R_max)
    G = 1.0 / R_clipped
    G_min = 1.0 / R_max
    G_max = 1.0 / R_min
    denominator = G_max - G_min + 1e-20
    G_norm = (G - G_min) / denominator
    G_norm = np.clip(G_norm, 0.0, 1.0)
    
    state = np.zeros_like(G_norm, dtype=int)
    state[(G_norm >= 0.75)] = 3
    state[(G_norm >= 0.5) & (G_norm < 0.75)] = 2
    state[(G_norm >= 0.25) & (G_norm < 0.5)] = 1
    state[G_norm < 0.25] = 0
    
    return state

# Param
V_values = [0.2, 0.3, 0.4, 0.5, 0.6, 0.7]
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
R_on = 6e3  
R_off = 9e5 

hrs_data = solve_hrs(V_values, colors, R_off_initial=R_off)
lrs_data = solve_lrs(V_values, colors)

state_configs = [
    {'name': 'State0', 'data': hrs_data, 'line_style': '--', 'color': 'black'},
    {'name': 'State1', 'data': lrs_data['State1'], 'line_style': '-', 'color': 'green'},
    {'name': 'State2', 'data': lrs_data['State2'], 'line_style': '-', 'color': 'blue'},
    {'name': 'State3', 'data': lrs_data['State3'], 'line_style': '-', 'color': 'red'}
]

for config in state_configs:
    plt.figure(figsize=(12, 8))
    state_name = config['name']
    
    for V, color in zip(V_values, colors):
        t, R = config['data'][V]
        
        states = resistance_to_state(R, R_on, R_off)
        
        plt.semilogx(t, states, 
                     linestyle=config['line_style'],
                     color=color,
                     linewidth=1.8,
                     alpha=0.8,
                     label=f'V={V}V' if V in V_values else None)
    
    plt.xlim(1e-7, 1e3)
    plt.ylim(-0.2, 3.5)
    plt.yticks([0, 1, 2, 3], ['00 (0)', '01 (1)', '10 (2)', '11 (3)'])
    plt.xlabel('Stress Time (s)', fontsize=14)
    plt.ylabel('Conductance State', fontsize=14)
    plt.title(f'State Transition: {state_name}', fontsize=16)
    plt.legend(fontsize=12, loc='upper right', framealpha=0.95)
    plt.grid(True, which='both', linestyle=':', alpha=0.6)
    plt.savefig(f'State_Transition_{state_name}.png', dpi=350, bbox_inches='tight')
    plt.close()