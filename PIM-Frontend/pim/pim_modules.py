import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pim import wage_quantizer
import logging
from pim.partition import partition_matrix_multiply_with_ADC, get_partition_size

class QAttention(nn.Module):
    def __init__(self, dim, num_heads=4, qkv_bias=False, attn_drop=0, proj_drop=0,
                 wl_input=8,wl_weight=8, sub_arrary_rows=128, sub_array_cols=128, cellBit=2, partitioned_by_crossbar=False,
                 ADC_effects=True, device_effects=True, ADCprecision=5,
                 d2dVari=0, nonlinearityLTP=1.75, nonlinearityLTD=1.46, max_level=4,
                 c2cVari=0):
        super().__init__()
        self.wl_weight = wl_weight
        self.wl_input = wl_input
        self.sub_arrary_rows = sub_arrary_rows
        self.sub_array_cols = sub_array_cols
        self.cellBit = cellBit
        assert dim % num_heads == 0
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5
        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        self.ADC_effects = ADC_effects
        self.device_effects = device_effects
        self.ADCprecision = ADCprecision
        self.d2dVari = d2dVari
        self.nonlinearityLTP = nonlinearityLTP
        self.nonlinearityLTD = nonlinearityLTD
        self.max_level = max_level
        self.c2cVari = c2cVari
        self.kt_weight_lasttime = None
        self.v_weight_lasttime = None
        self.partitioned_by_crossbar = partitioned_by_crossbar
        self.partition_size = get_partition_size(self.sub_arrary_rows, self.sub_array_cols, self.cellBit, self.wl_weight)
        # option1: device effects + ADC effects(not partitioned by crossbar)
        # option2: no device effects + ADC effects(partitioned by crossbar)
        # option3: device effects + no ADC effects

    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv.unbind(0)
        # print("q shape:", q.shape)
        kt = k.transpose(-1, -2)
        if self.device_effects:
            if self.kt_weight_lasttime is None:
                kt_weight_lasttime = torch.zeros_like(kt)
            else:
                kt_weight_lasttime = self.kt_weight_lasttime
            kt = wage_quantizer.NonlinearWeightUpdateWithD2DVariations(
                origin=kt_weight_lasttime, param=kt, d2dVari=self.d2dVari,
                nonlinearityLTP=self.nonlinearityLTP, nonlinearityLTD=self.nonlinearityLTD, max_level=self.max_level
            )
            # Dot Product in a row-by-row fashion. Equal to: q_dp_k = q @ kt
            if self.c2cVari != 0:
                dp_per_row = []
                for i in range(q.shape[0]):
                    row_result = q[i, :] @ kt
                    kt = wage_quantizer.NonlinearWeightUpdateWithC2CVariations(x=kt, sigmaC2C=self.c2cVari)
                    dp_per_row.append(row_result)
                self.kt_weight_lasttime = kt
                q_dp_k = torch.stack(dp_per_row, dim=0)
            else:
                q_dp_k = q @ kt
        elif self.ADC_effects and self.partitioned_by_crossbar:
            # q: [batch_size, num_heads, seq_len, head_dim]
            # kt: [batch_size, num_heads, head_dim, seq_len]
            B, H, S, D = q.shape
            q_dp_k = torch.zeros((B, H, S, S), device=q.device)
            
            for b in range(B):
                for h in range(H):
                    current_q = q[b, h]    # [seq_len, head_dim]
                    current_kt = kt[b, h]  # [head_dim, seq_len]
                    # add batch dimension and calculate the result of current head
                    current_result = partition_matrix_multiply_with_ADC(
                        current_q.unsqueeze(0),  # [1, seq_len, head_dim]
                        current_kt,              # [head_dim, seq_len]
                        self.partition_size[0], 
                        self.partition_size[1], 
                        enable_ADC=self.ADC_effects, 
                        ADCprecision=self.ADCprecision
                    )
                    q_dp_k[b, h] = current_result.squeeze(0)
        else:
            q_dp_k = q @ kt
        if self.ADC_effects and not self.partitioned_by_crossbar:
            q_dp_k = wage_quantizer.LinearQuantizeOut(q_dp_k, self.ADCprecision)
        attn = q_dp_k * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)
        # print("attn shape:", attn.shape)
        if self.device_effects:
            if self.v_weight_lasttime is None:
                v_weight_lasttime = torch.zeros_like(v)
            else:
                v_weight_lasttime = self.v_weight_lasttime
            v = wage_quantizer.NonlinearWeightUpdateWithD2DVariations(
                origin=v_weight_lasttime, param=v, d2dVari=self.d2dVari,
                nonlinearityLTP=self.nonlinearityLTP, nonlinearityLTD=self.nonlinearityLTD, max_level=self.max_level
            )
            # Dot Product in a row-by-row fashion. Equal to: attn_dp_v = attn @ v
            if self.c2cVari != 0:
                dp_per_row = []
                for i in range(attn.shape[0]):
                    row_result = attn[i, :] @ v
                    v = wage_quantizer.NonlinearWeightUpdateWithC2CVariations(x=v, sigmaC2C=self.c2cVari)
                    dp_per_row.append(row_result)
                self.v_weight_lasttime = v
                attn_dp_v = torch.stack(dp_per_row, dim=0)
            else:
                attn_dp_v = attn @ v
        elif self.ADC_effects and self.partitioned_by_crossbar:
            # attn: [batch_size, num_heads, seq_len, seq_len]
            # v: [batch_size, num_heads, seq_len, head_dim]
            B, H, S, _ = attn.shape
            attn_dp_v = torch.zeros((B, H, S, v.shape[-1]), device=attn.device)
            
            for b in range(B):
                for h in range(H):
                    current_attn = attn[b, h]    # [seq_len, seq_len]
                    current_v = v[b, h]          # [seq_len, head_dim]
                    # add batch dimension and calculate the result of current head
                    current_result = partition_matrix_multiply_with_ADC(
                        current_attn.unsqueeze(0),  # [1, seq_len, seq_len]
                        current_v,                  # [seq_len, head_dim]
                        self.partition_size[0], 
                        self.partition_size[1], 
                        enable_ADC=self.ADC_effects, 
                        ADCprecision=self.ADCprecision
                    )
                    attn_dp_v[b, h] = current_result.squeeze(0)
        else:
            attn_dp_v = attn @ v
        if self.ADC_effects and not self.partitioned_by_crossbar:
            attn_dp_v = wage_quantizer.LinearQuantizeOut(attn_dp_v, self.ADCprecision)
        x = (attn_dp_v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x

class QLinear(nn.Linear):
    def __init__(self, in_features, out_features, bias=False,
	             wl_input=8,wl_weight=8, sub_arrary_rows=128, sub_array_cols=128, partitioned_by_crossbar=False,
                 ADC_effects=True, device_effects=True, onoffratio=10,cellBit=2,ADCprecision=5,vari=0,t=0,v=0,detect=0,target=0,parallelRead=128,
                 name ='Qlinear'):
        super(QLinear, self).__init__(in_features, out_features, bias)
        self.wl_weight = wl_weight
        self.wl_input = wl_input
        self.sub_arrary_rows = sub_arrary_rows
        self.sub_array_cols = sub_array_cols
        self.nonideal_effects = (ADC_effects or device_effects)
        self.ADC_effects = ADC_effects
        self.device_effects = device_effects
        self.onoffratio = onoffratio
        self.cellBit = cellBit
        self.parallelRead = parallelRead
        self.ADCprecision = ADCprecision
        self.vari = vari
        self.t = t
        self.v = v
        self.detect = detect
        self.target = target
        self.name = name
        self.VGG_specified = False
        self.partitioned_by_crossbar = partitioned_by_crossbar
        self.partition_size = get_partition_size(self.sub_arrary_rows, self.sub_array_cols, self.cellBit, self.wl_weight)

    def forward(self, input):

        # logging.info(f'nonideal_effects: { self.nonideal_effects}')
        weight = self.weight
        outputOrignal = F.linear(input, weight, self.bias)
        output = torch.zeros_like(outputOrignal)

        bitWeight = int(self.wl_weight)
        bitActivation = int(self.wl_input)

        if self.nonideal_effects and self.VGG_specified:
            # retention
            weight = wage_quantizer.Retention(weight,self.t,self.v,self.detect,self.target)
            # set parameters for Hardware Inference
            onoffratio = self.onoffratio
            upper = 1
            lower = 1/onoffratio
            output = torch.zeros_like(outputOrignal)
            cellRange = 2**self.cellBit   # cell precision is 4
            # Now consider on/off ratio
            dummyP = torch.zeros_like(weight)
            dummyP[:,:] = (cellRange-1)*(upper+lower)/2
            # need to divide to different subArray
            assert self.sub_arrary_rows == self.sub_array_cols
            numSubArray = int(weight.shape[1]/self.sub_arrary_rows)

            if numSubArray == 0:
                mask = torch.zeros_like(weight)
                mask[:,:] = 1
                # quantize input into binary sequence
                inputQ = torch.round((2**bitActivation - 1)/1 * (input-0) + 0)
                outputIN = torch.zeros_like(outputOrignal)
                for z in range(bitActivation):
                    inputB = torch.fmod(inputQ, 2)
                    inputQ = torch.round((inputQ-inputB)/2)
                    # after get the spacial kernel, need to transfer floating weight [-1, 1] to binarized ones
                    X_decimal = torch.round((2**bitWeight - 1)/2 * (weight+1) + 0)*mask
                    outputP = torch.zeros_like(outputOrignal)
                    outputD = torch.zeros_like(outputOrignal)
                    for k in range (int(bitWeight/self.cellBit)):
                        remainder = torch.fmod(X_decimal, cellRange)*mask
                        X_decimal = torch.round((X_decimal-remainder)/cellRange)*mask
                        # Now also consider weight has on/off ratio effects
                        # Here remainder is the weight mapped to Hardware, so we introduce on/off ratio in this value
                        # the range of remainder is [0, cellRange-1], we truncate it to [lower, upper]
                        remainderQ = (upper-lower)*(remainder-0)+(cellRange-1)*lower   # weight cannot map to 0, but to Gmin
                        remainderQ = remainderQ + remainderQ*torch.normal(0., torch.full(remainderQ.size(),self.vari, device='cuda'))
                        outputPartial= F.linear(inputB, remainderQ*mask, self.bias)
                        outputDummyPartial= F.linear(inputB, dummyP*mask, self.bias)
                        # Add ADC quanization effects here !!!
                        outputPartialQ = wage_quantizer.LinearQuantizeOut(outputPartial, self.ADCprecision)
                        outputDummyPartialQ = wage_quantizer.LinearQuantizeOut(outputDummyPartial, self.ADCprecision)
                        scaler = cellRange**k
                        outputP = outputP + outputPartialQ*scaler*2/(1-1/onoffratio)
                        outputD = outputD + outputDummyPartialQ*scaler*2/(1-1/onoffratio)
                    scalerIN = 2**z
                    outputIN = outputIN + (outputP - outputD)*scalerIN
                output = output + outputIN/(2**bitActivation)
            else:
                inputQ = torch.round((2**bitActivation - 1)/1 * (input-0) + 0)
                outputIN = torch.zeros_like(outputOrignal)
                for z in range(bitActivation):
                    inputB = torch.fmod(inputQ, 2)
                    inputQ = torch.round((inputQ-inputB)/2)
                    outputP = torch.zeros_like(outputOrignal)
                    for s in range(numSubArray):
                        mask = torch.zeros_like(weight)
                        mask[:,(s*self.sub_arrary_rows):(s+1)*self.sub_arrary_rows] = 1
                        # after get the spacial kernel, need to transfer floating weight [-1, 1] to binarized ones
                        X_decimal = torch.round((2**bitWeight - 1)/2 * (weight+1) + 0)*mask
                        outputSP = torch.zeros_like(outputOrignal)
                        outputD = torch.zeros_like(outputOrignal)
                        for k in range (int(bitWeight/self.cellBit)):
                            remainder = torch.fmod(X_decimal, cellRange)*mask
                            X_decimal = torch.round((X_decimal-remainder)/cellRange)*mask
                            # Now also consider weight has on/off ratio effects
                            # Here remainder is the weight mapped to Hardware, so we introduce on/off ratio in this value
                            # the range of remainder is [0, cellRange-1], we truncate it to [lower, upper]*(cellRange-1)
                            remainderQ = (upper-lower)*(remainder-0)+(cellRange-1)*lower   # weight cannot map to 0, but to Gmin
                            remainderQ = remainderQ + remainderQ*torch.normal(0., torch.full(remainderQ.size(),self.vari, device='cuda'))
                            outputPartial= F.linear(inputB, remainderQ*mask, self.bias)
                            outputDummyPartial= F.linear(inputB, dummyP*mask, self.bias)
                            # Add ADC quanization effects here !!!
                            outputPartialQ = wage_quantizer.LinearQuantizeOut(outputPartial, self.ADCprecision)
                            outputDummyPartialQ = wage_quantizer.LinearQuantizeOut(outputDummyPartial, self.ADCprecision)
                            scaler = cellRange**k
                            outputSP = outputSP + outputPartialQ*scaler*2/(1-1/onoffratio)
                            outputD = outputD + outputDummyPartialQ*scaler*2/(1-1/onoffratio)
                        outputSP = outputSP - outputD  # minus dummy column
                        outputP = outputP + outputSP
                    scalerIN = 2**z
                    outputIN = outputIN + outputP*scalerIN
                output = output + outputIN/(2**bitActivation)
            output = output/(2**bitWeight)
        elif self.nonideal_effects:
            # option1: device effects + ADC effects(not partitioned by crossbar)
            # option2: device effects + ADC effects(partitioned by crossbar)
            # option3: device effects + no ADC effects
            # option4: no device effects + ADC effects(partitioned by crossbar)
            # option5: no device effects + ADC effects(not partitioned by crossbar)
            weight = self.weight #TODO: add quantization for weights
            if self.device_effects:
                weight = wage_quantizer.Retention(weight,self.t,self.v,self.detect,self.target)
            input = input #TODO: add quantization for inputs
            # print("input shape:", input.shape)
            if not self.ADC_effects:
                output= F.linear(input, weight, self.bias)
            elif self.ADC_effects and self.partitioned_by_crossbar:
                weight = weight.t()
                output = partition_matrix_multiply_with_ADC(input, weight, self.partition_size[0], self.partition_size[1], enable_ADC=self.ADC_effects, ADCprecision=self.ADCprecision)
                if self.bias is not None:
                    output += self.bias.reshape(1, 1, -1)
            else:   # ADC effects(not partitioned by crossbar)
                output = F.linear(input, weight, self.bias)
                output = wage_quantizer.LinearQuantizeOut(output, self.ADCprecision)
        else:
            # original linear
            output = F.linear(input, weight, self.bias)

        # logging.info('1 '+str(F.linear(input, weight, self.bias)))
        # logging.info('2 '+str(output))
        # logging.info(' ')
        
        return output