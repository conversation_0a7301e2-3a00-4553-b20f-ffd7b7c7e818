import numpy as np
from scipy.integrate import solve_ivp
import matplotlib.pyplot as plt

def solve_hrs(V_values, colors, R_off_initial=9e5):
    # Params
    g1 = 0.3e-9          # 0.3 nm → m
    V0 = 0.047           # 47 mV → V
    v0 = 0.1             # m/s
    L = 6e-9             # 6 nm → m
    a0 = 0.25e-9         # 0.25 nm → m
    Ea = 0.8 * 1.602e-19 # 0.8 eV → J (1 eV = 1.602e-19 J)
    beta = 1
    g0 = 0.6e-9          # initial gap 0.6 nm → m
    Vout = 0.05          # 50 mV → V
    T = 298              # K
    gamma0 = 25          
    k = 1.380649e-23     # Boltzmann constant (J/K)
    q = 1.602e-19        # Electron charge (C)

    exp_term_initial = np.exp(-g0 / g1)
    sinh_term_initial = np.sinh(Vout / V0)
    I0 = Vout / (R_off_initial * exp_term_initial * sinh_term_initial)
    print(f"I0 = {I0:.2e} A")

    # Fit a linear model based on the provided g_min data.
    V_fit = np.array([0.5, 0.6, 0.7]) 
    g_min_fit = np.array([0.19, 0.16, 0.12]) * 1e-9  
    coefficients = np.polyfit(V_fit, g_min_fit, 1)   # First-order polynomial fitting
    a_slope, b_intercept = coefficients

    def get_g_min(V):
        g_min = a_slope * V + b_intercept
        return max(g_min, 0)  # Avoid negative values.

    V_values = [0.2, 0.3, 0.4, 0.5, 0.6, 0.7]
    
    t_eval = np.logspace(-7, 0, 1000)
    results = {}

    for V, color in zip(V_values, colors):
        g_min = get_g_min(V)
        print(f"V = {V} V → g_min = {g_min*1e9:.2f} nm")
        
        def dgdt(t, g):
            if g <= g_min or g <= 0:
                return 0.0  # Stop changing when the gap reaches the lower limit.
            gamma = gamma0 - beta * (g / g0)**3
            sinh_arg = (gamma * a0 / L) * (q * V) / (k * T)
            exp_term = np.exp(-Ea / (k * T))  
            return -v0 * exp_term * np.sinh(sinh_arg)
        
        sol = solve_ivp(dgdt, [t_eval[0], t_eval[-1]], [g0], 
                        t_eval=t_eval, method='Radau', rtol=1e-6, atol=1e-20)
        g_t = sol.y[0]
        
        exponent = -g_t / g1
        sinh_term = np.sinh(Vout / V0)
        denominator = I0 * np.exp(exponent) * sinh_term
        R = Vout / denominator  # 公式1
        results[V] = (sol.t, R)

    return results

if __name__ == "__main__":
    V_values = [0.2, 0.3, 0.4, 0.5, 0.6, 0.7]
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

    # User input
    R_off_initial = 9e5

    results = solve_hrs(V_values, colors=colors, R_off_initial=R_off_initial)

    plt.figure(figsize=(10, 6))
    for V, color in zip(V_values, colors):
        t, R = results[V]
        plt.loglog(t, R, color=color, linewidth=2, label=f'V = {V} V')
    plt.xlim(1e-7, 1) 
    plt.xlabel('Stress Time (s)', fontsize=12)
    plt.ylabel('Resistance (Ω)', fontsize=12)
    plt.title('Resistance vs Stress Time (State0)', fontsize=14)
    plt.legend(fontsize=10, loc='upper right')
    plt.grid(True, which='both', linestyle='--', alpha=0.7)
    plt.savefig('Resistance_State0.png', dpi=300, bbox_inches='tight')
    plt.show()