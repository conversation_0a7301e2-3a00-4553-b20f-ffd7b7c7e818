import argparse
import os
import time
import math
import logging
import numpy as np
import sys

import torch
import torch.nn as nn
from pathlib import Path

import timm
from timm.data import Mixup
from timm.models import create_model
from timm.loss import LabelSmoothingCrossEntropy, SoftTargetCrossEntropy
from timm.scheduler import create_scheduler
from timm.optim import create_optimizer
from timm.utils import NativeScaler, get_state_dict, ModelEma, accuracy
from timm.models.vision_transformer import Attention
# from models import *
from ivit.utils import *
from pim.pim_modules import QLinear, QAttention


parser = argparse.ArgumentParser(description="I-ViT")

parser.add_argument("--model", default='deit_tiny',
                    choices=['vit_base', 'vit_large', 'deit_tiny', 'deit_small', 'deit_base', 
                             'swin_tiny', 'swin_small', 'swin_base'],
                    help="model")
parser.add_argument('--data', metavar='DIR', default='/dataset/imagenet/',
                    help='path to dataset')
parser.add_argument('--data-set', default='IMNET', choices=['CIFAR', 'IMNET'],
                    type=str, help='Image Net dataset path')
parser.add_argument("--nb-classes", default=1000, type=int, help="number of classes")
parser.add_argument('--input-size', default=224, type=int, help='images input size')
parser.add_argument("--device", default="cuda", type=str, help="device")
parser.add_argument("--print-freq", default=1000,
                    type=int, help="print frequency")
parser.add_argument("--seed", default=0, type=int, help="seed")
parser.add_argument('--output-dir', type=str, default='results/',
                    help='path to save log and quantized model')

parser.add_argument('--resume', default='', help='resume from checkpoint; timm/vit_base_patch16_224.augreg2_in21k_ft_in1k')
parser.add_argument('--start_epoch', default=0, type=int, metavar='N',
                    help='start epoch')
parser.add_argument('--batch-size', default=128, type=int)
parser.add_argument('--epochs', default=90, type=int)
parser.add_argument('--num-workers', default=8, type=int)
parser.add_argument('--pin-mem', action='store_true',
                    help='Pin CPU memory in DataLoader for more efficient (sometimes) transfer to GPU.')
parser.add_argument('--no-pin-mem', action='store_false', dest='pin_mem',
                    help='')
parser.set_defaults(pin_mem=True)

parser.add_argument('--drop', type=float, default=0.0, metavar='PCT',
                    help='Dropout rate (default: 0.)')
parser.add_argument('--drop-path', type=float, default=0.1, metavar='PCT',
                    help='Drop path rate (default: 0.1)')

parser.add_argument('--model-ema', action='store_true')
parser.add_argument('--no-model-ema', action='store_false', dest='model_ema')
parser.add_argument('--model-ema-decay', type=float, default=0.99996, help='')
parser.add_argument('--model-ema-force-cpu', action='store_true', default=False, help='')

# Optimizer parameters
parser.add_argument('--opt', default='adamw', type=str, metavar='OPTIMIZER',
                    help='Optimizer (default: "adamw"')
parser.add_argument('--opt-eps', default=1e-8, type=float, metavar='EPSILON',
                    help='Optimizer Epsilon (default: 1e-8)')
parser.add_argument('--opt-betas', default=None, type=float, nargs='+', metavar='BETA',
                    help='Optimizer Betas (default: None, use opt default)')
parser.add_argument('--clip-grad', type=float, default=None, metavar='NORM',
                    help='Clip gradient norm (default: None, no clipping)')
parser.add_argument('--momentum', type=float, default=0.9, metavar='M',
                    help='SGD momentum (default: 0.9)')
parser.add_argument('--weight-decay', type=float, default=1e-4,
                    help='weight decay (default: 1e-4)')
# Learning rate schedule parameters
parser.add_argument('--sched', default='cosine', type=str, metavar='SCHEDULER',
                    help='LR scheduler (default: "cosine"')
parser.add_argument('--lr', type=float, default=1e-6, metavar='LR',
                    help='learning rate (default: 1e-6)')
parser.add_argument('--lr-noise', type=float, nargs='+', default=None, metavar='pct, pct',
                    help='learning rate noise on/off epoch percentages')
parser.add_argument('--lr-noise-pct', type=float, default=0.67, metavar='PERCENT',
                    help='learning rate noise limit percent (default: 0.67)')
parser.add_argument('--lr-noise-std', type=float, default=1.0, metavar='STDDEV',
                    help='learning rate noise std-dev (default: 1.0)')
parser.add_argument('--warmup-lr', type=float, default=1e-6, metavar='LR',
                    help='warmup learning rate (default: 1e-6)')
parser.add_argument('--min-lr', type=float, default=5e-7, metavar='LR',
                    help='lower lr bound for cyclic schedulers that hit 0 (1e-5)')

parser.add_argument('--decay-epochs', type=float, default=30, metavar='N',
                    help='epoch interval to decay LR')
parser.add_argument('--warmup-epochs', type=int, default=0, metavar='N',
                    help='epochs to warmup LR, if scheduler supports')
parser.add_argument('--cooldown-epochs', type=int, default=10, metavar='N',
                    help='epochs to cooldown LR at min_lr, after cyclic schedule ends')
parser.add_argument('--patience-epochs', type=int, default=10, metavar='N',
                    help='patience epochs for Plateau LR scheduler (default: 10')
parser.add_argument('--decay-rate', '--dr', type=float, default=0.1, metavar='RATE',
                    help='LR decay rate (default: 0.1)')

# Augmentation parameters
parser.add_argument('--color-jitter', type=float, default=0.4, metavar='PCT',
                    help='Color jitter factor (default: 0.4)')
parser.add_argument('--aa', type=str, default='rand-m9-mstd0.5-inc1', metavar='NAME',
                    help='Use AutoAugment policy. "v0" or "original". " + \
                           "(default: rand-m9-mstd0.5-inc1)'),
parser.add_argument('--smoothing', type=float, default=0.1, help='Label smoothing (default: 0.1)')
parser.add_argument('--train-interpolation', type=str, default='bicubic',
                    help='Training interpolation (random, bilinear, bicubic default: "bicubic")')

# * Random Erase params
parser.add_argument('--reprob', type=float, default=0.25, metavar='PCT',
                    help='Random erase prob (default: 0.25)')
parser.add_argument('--remode', type=str, default='pixel',
                    help='Random erase mode (default: "pixel")')
parser.add_argument('--recount', type=int, default=1,
                    help='Random erase count (default: 1)')
parser.add_argument('--resplit', action='store_true', default=False,
                    help='Do not random erase first (clean) augmentation split')

# * Mixup params
parser.add_argument('--mixup', type=float, default=0.8,
                    help='mixup alpha, mixup enabled if > 0. (default: 0.8)')
parser.add_argument('--cutmix', type=float, default=1.0,
                    help='cutmix alpha, cutmix enabled if > 0. (default: 1.0)')
parser.add_argument('--cutmix-minmax', type=float, nargs='+', default=None,
                    help='cutmix min/max ratio, overrides alpha and enables cutmix if set (default: None)')
parser.add_argument('--mixup-prob', type=float, default=1.0,
                    help='Probability of performing mixup or cutmix when either/both is enabled')
parser.add_argument('--mixup-switch-prob', type=float, default=0.5,
                    help='Probability of switching to cutmix when both mixup and cutmix enabled')
parser.add_argument('--mixup-mode', type=str, default='batch',
                    help='How to apply mixup/cutmix params. Per "batch", "pair", or "elem"')

parser.add_argument('--best-acc1', type=float, default=0, help='best_acc1')

# CIM macro params 
parser.add_argument("--mapping-type", default="hybrid", type=str, help="pure_rram / hybrid / ideal")
parser.add_argument('--ADC-effects', action='store_true', default=False, help='consider ADC quantization effects')
parser.add_argument('--partitioned-by-crossbar', action='store_true', default=False, help='evaluate ADC effects considering weights partitioned by crossbar')
parser.add_argument('--device-effects', action='store_true', default=False, help='consider device effects')

parser.add_argument('--wl-input', type=int, default=8, help='')
parser.add_argument('--wl-weight', type=int, default=8, help='')

parser.add_argument('--onoffratio', type=float, default=10, help='device on/off ratio (e.g. Gmax/Gmin = 3)')
parser.add_argument('--cellBit', type=int, default=2, help='ACIM cell precision (e.g. 4-bit/cell)')
parser.add_argument('--sub-array-rows', type=int, default=128, help='row size of subArray (e.g. 128)')
parser.add_argument('--sub-array-cols', type=int, default=128, help='column size of subArray (e.g. 128)')
parser.add_argument('--parallelRead', type=int, default=128, help='number of rows read in parallel (<= subArray e.g. 32)')
parser.add_argument('--ADCprecision', type=int, default=5, help='ADC precision (e.g. 5-bit)')

parser.add_argument('--vari', type=float, default=0, help='conductance variation (e.g. 0.1 standard deviation to generate random variation)')
parser.add_argument('--t', type=float, default=0, help='retention time, set 0 if not considering it')
parser.add_argument('--v', type=float, default=0, help='drift coefficient')
parser.add_argument('--detect', type=int, default=0, help='if 1, fixed-direction drift, if 0, random drift')
parser.add_argument('--target', type=float, default=0, help='drift target for fixed-direction drift, range 0-1')

# for pure_rram
parser.add_argument('--nonlinearityLTP', type=float, default=1.75, help='nonlinearity in LTP')
parser.add_argument('--nonlinearityLTD', type=float, default=1.46, help='nonlinearity in LTD (negative if LTP and LTD are asymmetric)')
parser.add_argument('--d2dVari', type=float, default=0.0, help='device-to-device variation')
parser.add_argument('--c2cVari', type=float, default=0.003, help='cycle-to-cycle variation')

# def str2model(name):
#     d = {'vit_base': vit_base_patch16_224,
#          'vit_large': vit_large_patch16_224,
#          'deit_tiny': deit_tiny_patch16_224,
#          'deit_small': deit_small_patch16_224,
#          'deit_base': deit_base_patch16_224,
#          'swin_tiny': swin_tiny_patch4_window7_224,
#          'swin_small': swin_small_patch4_window7_224,
#          'swin_base': swin_base_patch4_window7_224,
#          }
#     print('Model: %s' % d[name].__name__)
#     return d[name]

def str2model(name):
    d = {'vit_base': 'vit_base_patch16_224',
         'vit_large': 'vit_large_patch16_224',
         'deit_tiny': 'deit_tiny_patch16_224',
         'deit_small': 'deit_small_patch16_224',
         'deit_base': 'deit_base_patch16_224',
         'swin_tiny': 'swin_tiny_patch4_window7_224',
         'swin_small': 'swin_small_patch4_window7_224',
         'swin_base': 'swin_base_patch4_window7_224',
         }
    return d[name]

def main():
    args = parser.parse_args()
    args.max_level = 2**args.cellBit # Maximum number of conductance states during weight update (floor(log2(max_level))=cellBit)
    
    import warnings
    warnings.filterwarnings('ignore')
    current_time = time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime())
    log_file_name = f"log_{current_time}.log"
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    logging.basicConfig(format='%(asctime)s - %(message)s',
                        datefmt='%d-%b-%y %H:%M:%S', filename=os.path.join(args.output_dir, log_file_name))
    logging.getLogger().setLevel(logging.INFO)
    logging.getLogger().addHandler(logging.StreamHandler())
    # logging.info(args)
    logging.info(str(sys.argv))
    logging.info("Program arguments: (For those are different from the default value)")
    for arg, value in vars(args).items():
        default_value = parser.get_default(arg)
        if value != default_value: 
            logging.info(f"{arg}: {value}")

    seed = args.seed
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    np.random.seed(seed)
    torch.backends.cudnn.benchmark = True
    # torch.cuda.memory.max_split_size_mb = 128
    device = torch.device(args.device)

    # Model
    model = create_model(str2model(args.model), pretrained=True, num_classes=args.nb_classes)
    # model = str2model(args.model)(pretrained=True,
    #                               num_classes=args.nb_classes,
    #                               drop_rate=args.drop,
    #                               drop_path_rate=args.drop_path)
    
    if (args.mapping_type=='ideal'):
        pass
    else:   # hybrid / pure_rram
        if (args.mapping_type=='pure_rram'):
            for name, module in model.named_modules():
                logging.info(f"Module Name: {name}, Module Type: {type(module).__name__}")
                if isinstance(module, Attention):
                    parent_name = '.'.join(name.split('.')[:-1]) 
                    parent_module = model.get_submodule(parent_name) if parent_name else model
                    logging.info(f"parent Name: {parent_name}, Module name: {name.split('.')[-1]}")

                    new_layer = QAttention(dim=module.num_heads*module.head_dim, num_heads=module.num_heads, qkv_bias=(module.qkv.bias is not None), attn_drop=module.attn_drop.p, proj_drop=module.proj_drop.p,
                        wl_input=args.wl_input,wl_weight=args.wl_weight, sub_arrary_rows=args.sub_array_rows, sub_array_cols=args.sub_array_cols, cellBit=args.cellBit, partitioned_by_crossbar=args.partitioned_by_crossbar,
                        ADC_effects=args.ADC_effects, device_effects=args.device_effects, ADCprecision=args.ADCprecision,
                        d2dVari=args.d2dVari, nonlinearityLTP=args.nonlinearityLTP, nonlinearityLTD=args.nonlinearityLTD, max_level=args.max_level,
                        c2cVari=args.c2cVari)
                    new_layer.qkv.weight.data = module.qkv.weight.data.clone()
                    if module.qkv.bias is not None:
                        new_layer.qkv.bias.data = module.qkv.bias.data.clone()
                    new_layer.proj.weight.data = module.proj.weight.data.clone()
                    if module.proj.bias is not None:
                        new_layer.proj.bias.data = module.proj.bias.data.clone()
                    
                    setattr(parent_module, name.split('.')[-1], new_layer)
            logging.info('Finished replacing modules (Attention)...')
            for name, module in model.named_modules():
                logging.info(f"Module Name: {name}, Module Type: {type(module).__name__}")

        for name, module in model.named_modules():
            logging.info(f"Module Name: {name}, Module Type: {type(module).__name__}")
            if isinstance(module, nn.Linear):
                parent_name = '.'.join(name.split('.')[:-1]) 
                parent_module = model.get_submodule(parent_name) if parent_name else model
                logging.info(f"parent Name: {parent_name}, Module name: {name.split('.')[-1]}")

                new_layer = QLinear(in_features=module.in_features, out_features=module.out_features,bias=(module.bias is not None),
                    wl_input=args.wl_input,wl_weight=args.wl_weight, sub_arrary_rows=args.sub_array_rows, sub_array_cols=args.sub_array_cols, partitioned_by_crossbar=args.partitioned_by_crossbar,
                    ADC_effects=args.ADC_effects, device_effects=args.device_effects,
                    onoffratio=args.onoffratio,cellBit=args.cellBit,parallelRead=args.parallelRead,ADCprecision=args.ADCprecision,
                    vari=args.vari,t=args.t,v=args.v,detect=args.detect,target=args.target,
                    name=name)
                new_layer.weight.data = module.weight.data.clone()
                if module.bias is not None:
                    new_layer.bias.data = module.bias.data.clone()
                
                setattr(parent_module, name.split('.')[-1], new_layer)
        logging.info('Finished replacing modules (Linear)...')

    for name, module in model.named_modules():
        logging.info(f"Module Name: {name}, Module Type: {type(module).__name__}")

    model.to(device)

    # Dataset
    train_loader, val_loader = dataloader(args)
    
    if args.resume:
        if args.resume.startswith('https'):
            checkpoint = torch.hub.load_state_dict_from_url(
                args.resume, map_location=device, check_hash=True)
        else:
            checkpoint = torch.load(args.resume, map_location=device)
        
        model.load_state_dict(checkpoint)
    
    criterion_v = nn.CrossEntropyLoss()
    acc = validate(args, val_loader, model, criterion_v, device)
    logging.info(f'Acc: {acc}')

def validate(args, val_loader, model, criterion, device):
    batch_time = AverageMeter('Time', ':6.3f')
    losses = AverageMeter('Loss', ':.5e')
    top1 = AverageMeter('Acc@1', ':6.5f')
    top5 = AverageMeter('Acc@5', ':6.5f')
    progress = ProgressMeter(
        len(val_loader),
        [batch_time, losses, top1, top5],
        prefix='Test: ')

    # switch to evaluate mode
    model.eval()
    # freeze_model(model)

    end = time.time()
    for i, (data, target) in enumerate(val_loader):
        data = data.to(device, non_blocking=True)
        target = target.to(device, non_blocking=True)

        with torch.no_grad():
            output = model(data)
            loss = criterion(output, target)

        # measure accuracy and record loss
        prec1, prec5 = accuracy(output, target, topk=(1, 5))
        losses.update(loss.data.item(), data.size(0))
        top1.update(prec1.data.item(), data.size(0))
        top5.update(prec5.data.item(), data.size(0))

        # measure elapsed time
        batch_time.update(time.time() - end)
        end = time.time()

        if i % args.print_freq == 0:
            progress.display(i)

    print(" * Prec@1 {top1.avg:.5f} Prec@5 {top5.avg:.5f}".format(top1=top1, top5=top5))
    logging.info(" * Prec@1 {top1.avg:.5f} Prec@5 {top5.avg:.5f}".format(top1=top1, top5=top5))
    return top1.avg

class AverageMeter(object):
    """Computes and stores the average and current value"""

    def __init__(self, name, fmt=':f'):
        self.name = name
        self.fmt = fmt
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count

    def __str__(self):
        fmtstr = '{name} {val' + self.fmt + '} ({avg' + self.fmt + '})'
        return fmtstr.format(**self.__dict__)


class ProgressMeter(object):
    def __init__(self, num_batches, meters, prefix=""):
        self.batch_fmtstr = self._get_batch_fmtstr(num_batches)
        self.meters = meters
        self.prefix = prefix

    def display(self, batch):
        entries = [self.prefix + self.batch_fmtstr.format(batch)]
        entries += [str(meter) for meter in self.meters]
        logging.info('\t'.join(entries))

    def _get_batch_fmtstr(self, num_batches):
        num_digits = len(str(num_batches // 1))
        fmt = '{:' + str(num_digits) + 'd}'
        return '[' + fmt + '/' + fmt.format(num_batches) + ']'



if __name__ == "__main__":
    main()