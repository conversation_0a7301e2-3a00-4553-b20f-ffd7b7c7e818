================================================================================
Exponential Map Convergence Analysis
================================================================================

Small angles (0.01 to 0.5 radians):
--------------------------------------------------

Magnitude: 0.010 rad (0.6°)
Iterations | Theta Error | Rel <PERSON>r (%) | Matrix Error
-------------------------------------------------------
         1 |    9.90e-01 |   9900.2500 |    2.24e-03
         2 |    4.90e-01 |   4900.6250 |    5.82e-04
         3 |    2.40e-01 |   2401.3124 |    1.47e-04
         5 |    5.30e-02 |    530.3191 |    9.13e-06
        10 |    7.14e-07 |      0.0071 |    3.37e-11

Magnitude: 0.100 rad (5.7°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    9.02e-01 |    902.5000 |    2.23e-02
         2 |    4.06e-01 |    406.2375 |    5.73e-03
         3 |    1.63e-01 |    162.9956 |    1.39e-03
         5 |    8.48e-03 |      8.4752 |    4.16e-05
        10 |    0.00e+00 |      0.0000 |    1.96e-09

Magnitude: 0.500 rad (28.6°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    5.62e-01 |    112.5000 |    9.73e-02
         2 |    1.49e-01 |     29.7794 |    1.96e-02
         3 |    1.71e-02 |      3.4166 |    2.01e-03
         5 |    7.96e-08 |      0.0000 |    3.06e-05
        10 |    1.11e-16 |      0.0000 |    3.06e-05

Medium angles (1.0 to 2.0 radians):
--------------------------------------------------

Magnitude: 1.000 rad (57.3°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    2.50e-01 |     25.0000 |    1.21e-01
         2 |    2.50e-02 |      2.5000 |    1.15e-02
         3 |    3.05e-04 |      0.0305 |    1.97e-03
         5 |    8.88e-16 |      0.0000 |    1.95e-03
        10 |    1.11e-16 |      0.0000 |    1.95e-03

Magnitude: 1.500 rad (85.9°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    6.25e-02 |      4.1667 |    6.65e-02
         2 |    1.25e-03 |      0.0833 |    2.23e-02
         3 |    5.20e-07 |      0.0000 |    2.20e-02
         5 |    0.00e+00 |      0.0000 |    2.20e-02
        10 |    0.00e+00 |      0.0000 |    2.20e-02

Magnitude: 2.000 rad (114.6°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    2.22e-16 |      0.0000 |    1.22e-01
         2 |    2.22e-16 |      0.0000 |    1.22e-01
         3 |    2.22e-16 |      0.0000 |    1.22e-01
         5 |    2.22e-16 |      0.0000 |    1.22e-01
        10 |    2.22e-16 |      0.0000 |    1.22e-01

Large angles (2.5 to 3.14 radians):
--------------------------------------------------

Magnitude: 2.500 rad (143.2°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    6.25e-02 |      2.5000 |    5.53e-01
         2 |    7.62e-04 |      0.0305 |    4.58e-01
         3 |    1.16e-07 |      0.0000 |    4.57e-01
         5 |    0.00e+00 |      0.0000 |    4.57e-01
        10 |    0.00e+00 |      0.0000 |    4.57e-01

Magnitude: 3.000 rad (171.9°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    2.50e-01 |      8.3333 |    2.12e+00
         2 |    9.62e-03 |      0.3205 |    1.36e+00
         3 |    1.54e-05 |      0.0005 |    1.34e+00
         5 |    0.00e+00 |      0.0000 |    1.34e+00
        10 |    0.00e+00 |      0.0000 |    1.34e+00

Magnitude: 3.140 rad (179.9°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    3.25e-01 |     10.3471 |    2.97e+00
         2 |    1.52e-02 |      0.4851 |    1.80e+00
         3 |    3.68e-05 |      0.0012 |    1.75e+00
         5 |    4.44e-16 |      0.0000 |    1.75e+00
        10 |    4.44e-16 |      0.0000 |    1.75e+00

Very large (4.0 to 6.0 radians):
--------------------------------------------------

Magnitude: 4.000 rad (229.2°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    1.00e+00 |     25.0000 |    1.93e+01
         2 |    1.00e-01 |      2.5000 |    8.05e+00
         3 |    1.22e-03 |      0.0305 |    7.16e+00
         5 |    4.44e-15 |      0.0000 |    7.15e+00
        10 |    0.00e+00 |      0.0000 |    7.15e+00

Magnitude: 5.000 rad (286.5°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    2.25e+00 |     45.0000 |    1.25e+02
         2 |    3.49e-01 |      6.9828 |    3.43e+01
         3 |    1.14e-02 |      0.2279 |    2.59e+01
         5 |    1.68e-11 |      0.0000 |    2.56e+01
        10 |    0.00e+00 |      0.0000 |    2.56e+01

Magnitude: 6.000 rad (343.8°)
Iterations | Theta Error | Rel Error (%) | Matrix Error
-------------------------------------------------------
         1 |    4.00e+00 |     66.6667 |    6.04e+02
         2 |    8.00e-01 |     13.3333 |    1.19e+02
         3 |    4.71e-02 |      0.7843 |    7.35e+01
         5 |    2.79e-09 |      0.0000 |    7.11e+01
        10 |    0.00e+00 |      0.0000 |    7.11e+01

================================================================================
SLAM-Specific Test Vectors
================================================================================
================================================================================
Exponential Map (SO(3)) Accuracy Test
================================================================================

Test Vector 1: phi = [0.1000, 0.0500, 0.0200]
Magnitude: 0.113578
------------------------------------------------------------
Iterations | Theta Error | Matrix Frobenius Error | Orthogonality Error
----------------------------------------------------------------------
         3 |    1.53e-01 |           1.55e-03 |        2.62e-04
         5 |    6.13e-03 |           3.83e-05 |        6.50e-06
        10 |    0.00e+00 |           4.22e-09 |        8.39e-09


Test Vector 2: phi = [0.5000, 0.3000, 0.1000]
Magnitude: 0.591608
------------------------------------------------------------
Iterations | Theta Error | Matrix Frobenius Error | Orthogonality Error
----------------------------------------------------------------------
         3 |    9.07e-03 |           1.48e-03 |        1.10e-03
         5 |    3.96e-09 |           8.40e-05 |        1.47e-04
        10 |    0.00e+00 |           8.40e-05 |        1.47e-04


Test Vector 3: phi = [1.0000, 0.8000, 0.5000]
Magnitude: 1.374773
------------------------------------------------------------
Iterations | Theta Error | Matrix Frobenius Error | Orthogonality Error
----------------------------------------------------------------------
         3 |    3.82e-06 |           1.31e-02 |        1.01e-02
         5 |    0.00e+00 |           1.31e-02 |        1.01e-02
        10 |    0.00e+00 |           1.31e-02 |        1.01e-02


Test Vector 4: phi = [0.0100, 0.0100, 0.0100]
Magnitude: 0.017321
------------------------------------------------------------
Iterations | Theta Error | Matrix Frobenius Error | Orthogonality Error
----------------------------------------------------------------------
         3 |    2.33e-01 |           2.54e-04 |        6.54e-06
         5 |    4.68e-02 |           1.55e-05 |        4.04e-07
        10 |    6.87e-10 |           1.11e-13 |        1.04e-13


Test Vector 5: phi = [2.0000, 1.5000, 1.0000]
Magnitude: 2.692582
------------------------------------------------------------
Iterations | Theta Error | Matrix Frobenius Error | Orthogonality Error
----------------------------------------------------------------------
         3 |    1.21e-06 |           7.09e-01 |        6.06e-01
         5 |    4.44e-16 |           7.09e-01 |        6.06e-01
        10 |    4.44e-16 |           7.09e-01 |        6.06e-01


================================================================================
Performance Impact Analysis
================================================================================
Sqrt Iterations | Total Operations | Relative Performance | Recommended Use
---------------------------------------------------------------------------
              1 |               60 |             250.0% | Very fast, low accuracy
              2 |               70 |             214.3% | Fast, moderate accuracy
              3 |               80 |             187.5% | Good balance
              5 |              100 |             150.0% | High accuracy
             10 |              150 |             100.0% | Maximum accuracy, slow

================================================================================
RECOMMENDATIONS
================================================================================
Based on SLAM applications:
• 3 iterations: Good for most SLAM scenarios, 40% performance gain
• 5 iterations: Current setting, balanced accuracy/performance
• 10 iterations: Overkill for SLAM, unnecessary precision

Suggested: Reduce Expmapping sqrt iterations from 10 to 3 or 5
Expected performance improvement: 20-40% for Expmapping operations
