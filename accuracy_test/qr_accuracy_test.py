#!/usr/bin/env python3
"""
QR Decomposition Accuracy Testing Tool
Tests the impact of different sqrt iteration counts on QR decomposition accuracy
"""

import numpy as np
import math

def newton_sqrt_iterations(x, iterations):
    """
    Calculate square root using <PERSON>'s method, simulating the iterative process in NeuroSIM
    Args:
        x: Value to take square root of
        iterations: Number of iterations
    Returns:
        Approximate square root value
    """
    if x <= 0:
        return 0
    
    # Initial guess
    guess = x / 2.0
    
    for i in range(iterations):
        # <PERSON>'s method: x_new = (x_old + x/x_old) / 2
        guess = (guess + x / guess) / 2.0
        
    return guess

def givens_rotation(a, b, sqrt_iterations):
    """
    Calculate Givens rotation parameters using specified sqrt iteration count
    Args:
        a, b: Matrix elements
        sqrt_iterations: Number of sqrt iterations
    Returns:
        Dictionary containing approximated and exact values with errors
    """
    # Calculate r = sqrt(a^2 + b^2)
    sum_squares = a*a + b*b
    r_approx = newton_sqrt_iterations(sum_squares, sqrt_iterations)
    r_exact = math.sqrt(sum_squares)
    
    # Calculate cos and sin
    cos_theta_approx = a / r_approx if r_approx != 0 else 1
    sin_theta_approx = b / r_approx if r_approx != 0 else 0
    
    cos_theta_exact = a / r_exact if r_exact != 0 else 1
    sin_theta_exact = b / r_exact if r_exact != 0 else 0
    
    return {
        'approx': (cos_theta_approx, sin_theta_approx, r_approx),
        'exact': (cos_theta_exact, sin_theta_exact, r_exact),
        'sqrt_error': abs(r_approx - r_exact),
        'cos_error': abs(cos_theta_approx - cos_theta_exact),
        'sin_error': abs(sin_theta_approx - sin_theta_exact)
    }

def test_qr_accuracy(matrix, iterations_list=[3, 5, 10]):
    """
    Test the impact of different iteration counts on overall QR decomposition accuracy
    """
    print("=" * 80)
    print("QR Decomposition Accuracy Test")
    print("=" * 80)
    
    # Original matrix
    A = np.array(matrix, dtype=float)
    print(f"Original Matrix A:")
    print(A)
    print()
    
    # NumPy standard QR decomposition as reference
    Q_ref, R_ref = np.linalg.qr(A)
    print("NumPy Reference QR Decomposition:")
    print(f"Q_ref:\n{Q_ref}")
    print(f"R_ref:\n{R_ref}")
    print()
    
    # Test different iteration counts
    for iterations in iterations_list:
        print(f"Testing {iterations} iterations:")
        print("-" * 40)
        
        # Simulate Givens QR decomposition
        A_copy = A.copy()
        Q_approx = np.eye(A.shape[0])
        
        # Column-wise elimination
        for col in range(min(A.shape[0]-1, A.shape[1])):
            for row in range(col+1, A.shape[0]):
                if abs(A_copy[row, col]) > 1e-10:  # If elimination is needed
                    # Calculate Givens rotation parameters
                    a = A_copy[col, col]
                    b = A_copy[row, col]
                    
                    givens_result = givens_rotation(a, b, iterations)
                    c = givens_result['approx'][0]  # cos
                    s = givens_result['approx'][1]  # sin
                    
                    # Apply Givens rotation to A
                    for k in range(A.shape[1]):
                        temp1 = c * A_copy[col, k] + s * A_copy[row, k]
                        temp2 = -s * A_copy[col, k] + c * A_copy[row, k]
                        A_copy[col, k] = temp1
                        A_copy[row, k] = temp2
                    
                    # Apply Givens rotation to Q
                    for k in range(A.shape[0]):
                        temp1 = c * Q_approx[k, col] + s * Q_approx[k, row]
                        temp2 = -s * Q_approx[k, col] + c * Q_approx[k, row]
                        Q_approx[k, col] = temp1
                        Q_approx[k, row] = temp2
        
        R_approx = A_copy
        
        # Calculate errors
        Q_error = np.linalg.norm(Q_approx - Q_ref)
        R_error = np.linalg.norm(R_approx - R_ref)
        reconstruction_error = np.linalg.norm(Q_approx @ R_approx - A)
        
        print(f"Q Error (Frobenius norm): {Q_error:.2e}")
        print(f"R Error (Frobenius norm): {R_error:.2e}")
        print(f"Reconstruction Error ||QR - A||: {reconstruction_error:.2e}")
        print()

def test_sqrt_convergence():
    """
    Test sqrt iteration convergence properties
    """
    print("=" * 80)
    print("Square Root Iteration Convergence Test")
    print("=" * 80)
    
    test_values = [1.0, 4.0, 9.0, 16.0, 25.0, 0.1, 0.01, 100.0, 1000.0]
    iterations_list = [1, 2, 3, 5, 10]
    
    for x in test_values:
        exact_sqrt = math.sqrt(x)
        print(f"x = {x}, Exact sqrt = {exact_sqrt:.8f}")
        print("Iterations | Approx Value | Absolute Error | Relative Error(%)")
        print("-" * 65)
        
        for iterations in iterations_list:
            approx_sqrt = newton_sqrt_iterations(x, iterations)
            abs_error = abs(approx_sqrt - exact_sqrt)
            rel_error = (abs_error / exact_sqrt) * 100 if exact_sqrt != 0 else 0
            
            print(f"{iterations:10d} | {approx_sqrt:12.8f} | {abs_error:14.2e} | {rel_error:13.4f}")
        print()

if __name__ == "__main__":
    # Test 1: sqrt convergence
    test_sqrt_convergence()
    
    # Test 2: 6x12 SLAM Jacobian matrix QR accuracy
    print("\n" + "="*80)
    print("SLAM Jacobian Matrix QR Decomposition Accuracy Test")
    print("="*80)
    
    # Simulate 6x12 SLAM Jacobian matrix (random but representative values)
    np.random.seed(42)  # Ensure reproducibility
    slam_matrix = np.random.randn(6, 12) * 0.1  # Small random values simulating real SLAM scenarios
    slam_matrix[0, 0] = 1.0  # Ensure matrix is not singular
    
    test_qr_accuracy(slam_matrix, iterations_list=[3, 5, 10])
    
    # Test 3: Extreme cases
    print("\n" + "="*80)
    print("Extreme Cases Test")
    print("="*80)
    
    # Test near-singular matrix
    extreme_matrix = np.array([
        [1.0, 0.0, 0.0],
        [0.0, 1e-6, 0.0],  # Very small value
        [0.0, 0.0, 1e6]     # Very large value
    ])
    
    test_qr_accuracy(extreme_matrix, iterations_list=[3, 5, 10])
