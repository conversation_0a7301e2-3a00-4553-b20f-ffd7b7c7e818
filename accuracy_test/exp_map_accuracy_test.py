#!/usr/bin/env python3
"""
Expmapping (Exponential Map) Accuracy Testing Tool
Tests the impact of different sqrt iteration counts on SO(3) exponential map accuracy
"""

import numpy as np
import math

def newton_sqrt_iterations(x, iterations):
    """
    Calculate square root using <PERSON>'s method, simulating the iterative process in NeuroSIM
    Args:
        x: Value to take square root of
        iterations: Number of iterations
    Returns:
        Approximate square root value
    """
    if x <= 0:
        return 0
    
    # Initial guess
    guess = x / 2.0
    
    for i in range(iterations):
        # <PERSON>'s method: x_new = (x_old + x/x_old) / 2
        guess = (guess + x / guess) / 2.0
        
    return guess

def skew_symmetric(v):
    """
    Create skew-symmetric matrix from 3D vector
    """
    return np.array([
        [0, -v[2], v[1]],
        [v[2], 0, -v[0]],
        [-v[1], v[0], 0]
    ])

def exp_map_approximated(phi, sqrt_iterations):
    """
    Compute exponential map using approximated sqrt (simulating NeuroSIM implementation)
    Args:
        phi: 3D rotation vector
        sqrt_iterations: Number of sqrt iterations
    Returns:
        3x3 rotation matrix
    """
    # Calculate θ = ||phi||
    theta_squared = np.dot(phi, phi)
    theta_approx = newton_sqrt_iterations(theta_squared, sqrt_iterations)
    theta_exact = math.sqrt(theta_squared)
    
    if theta_approx < 1e-8:
        # Small angle approximation
        return np.eye(3) + skew_symmetric(phi)
    
    # Calculate sin(θ)/θ using Taylor series
    theta2 = theta_approx * theta_approx
    sin_theta_over_theta = 1.0 - theta2/6.0 + theta2*theta2/120.0
    
    # Calculate (1-cos(θ))/θ² using Taylor series  
    one_minus_cos_over_theta2 = 0.5 - theta2/24.0
    
    # Create skew-symmetric matrix
    phi_hat = skew_symmetric(phi)
    
    # Rodriguez formula: R = I + sin(θ)/θ * φ^ + (1-cos(θ))/θ² * φ^²
    R = (np.eye(3) + 
         sin_theta_over_theta * phi_hat + 
         one_minus_cos_over_theta2 * np.dot(phi_hat, phi_hat))
    
    return R, theta_approx, theta_exact

def exp_map_exact(phi):
    """
    Compute exact exponential map using scipy/numpy
    """
    from scipy.spatial.transform import Rotation
    
    # Convert axis-angle to rotation matrix
    theta = np.linalg.norm(phi)
    if theta < 1e-8:
        return np.eye(3)
    
    axis = phi / theta
    r = Rotation.from_rotvec(phi)
    return r.as_matrix()

def test_exp_map_accuracy(test_vectors, iterations_list=[3, 5, 10]):
    """
    Test the impact of different sqrt iteration counts on exponential map accuracy
    """
    print("=" * 80)
    print("Exponential Map (SO(3)) Accuracy Test")
    print("=" * 80)
    
    for i, phi in enumerate(test_vectors):
        print(f"\nTest Vector {i+1}: phi = [{phi[0]:.4f}, {phi[1]:.4f}, {phi[2]:.4f}]")
        print(f"Magnitude: {np.linalg.norm(phi):.6f}")
        print("-" * 60)
        
        # Compute exact exponential map
        try:
            R_exact = exp_map_exact(phi)
        except ImportError:
            # Fallback if scipy is not available
            theta = np.linalg.norm(phi)
            if theta < 1e-8:
                R_exact = np.eye(3)
            else:
                phi_hat = skew_symmetric(phi)
                R_exact = (np.eye(3) + 
                          np.sin(theta)/theta * phi_hat + 
                          (1-np.cos(theta))/(theta*theta) * np.dot(phi_hat, phi_hat))
        
        print("Iterations | Theta Error | Matrix Frobenius Error | Orthogonality Error")
        print("-" * 70)
        
        # Test different iteration counts
        for iterations in iterations_list:
            R_approx, theta_approx, theta_exact = exp_map_approximated(phi, iterations)
            
            # Calculate errors
            theta_error = abs(theta_approx - theta_exact)
            matrix_error = np.linalg.norm(R_approx - R_exact, 'fro')
            
            # Check orthogonality (R^T * R should be identity)
            orthogonality_error = np.linalg.norm(R_approx.T @ R_approx - np.eye(3), 'fro')
            
            print(f"{iterations:10d} | {theta_error:11.2e} | {matrix_error:18.2e} | {orthogonality_error:15.2e}")
        
        print()

def test_exp_map_convergence():
    """
    Test convergence properties for different magnitude ranges
    """
    print("=" * 80)
    print("Exponential Map Convergence Analysis")
    print("=" * 80)
    
    # Test different magnitude ranges
    magnitude_ranges = [
        ("Small angles", [0.01, 0.1, 0.5]),           # Small rotations
        ("Medium angles", [1.0, 1.5, 2.0]),           # Medium rotations  
        ("Large angles", [2.5, 3.0, 3.14]),           # Large rotations (up to π)
        ("Very large", [4.0, 5.0, 6.0])               # Beyond π (multiple rotations)
    ]
    
    for range_name, magnitudes in magnitude_ranges:
        print(f"\n{range_name} ({magnitudes[0]} to {magnitudes[-1]} radians):")
        print("-" * 50)
        
        for magnitude in magnitudes:
            # Create random unit vector and scale by magnitude
            direction = np.random.randn(3)
            direction = direction / np.linalg.norm(direction)
            phi = direction * magnitude
            
            print(f"\nMagnitude: {magnitude:.3f} rad ({magnitude*180/math.pi:.1f}°)")
            print("Iterations | Theta Error | Rel Error (%) | Matrix Error")
            print("-" * 55)
            
            theta_exact = magnitude
            
            for iterations in [1, 2, 3, 5, 10]:
                R_approx, theta_approx, _ = exp_map_approximated(phi, iterations)
                
                theta_error = abs(theta_approx - theta_exact)
                rel_error = (theta_error / theta_exact) * 100 if theta_exact > 0 else 0
                
                # Compute exact for matrix comparison
                try:
                    R_exact = exp_map_exact(phi)
                    matrix_error = np.linalg.norm(R_approx - R_exact, 'fro')
                except:
                    matrix_error = 0  # Fallback
                
                print(f"{iterations:10d} | {theta_error:11.2e} | {rel_error:11.4f} | {matrix_error:11.2e}")

def performance_impact_analysis():
    """
    Analyze performance impact of different iteration counts
    """
    print("\n" + "=" * 80)
    print("Performance Impact Analysis")
    print("=" * 80)
    
    # Simulate the operation count for each iteration setting
    base_operations = 50  # Estimated base operations in Expmapping
    sqrt_operations_per_iter = 10  # Operations per sqrt iteration
    
    print("Sqrt Iterations | Total Operations | Relative Performance | Recommended Use")
    print("-" * 75)
    
    iteration_settings = [
        (1, "Very fast, low accuracy"),
        (2, "Fast, moderate accuracy"),  
        (3, "Good balance"),
        (5, "High accuracy"),
        (10, "Maximum accuracy, slow")
    ]
    
    for iterations, description in iteration_settings:
        total_ops = base_operations + iterations * sqrt_operations_per_iter
        relative_perf = 100.0 * (base_operations + 10 * sqrt_operations_per_iter) / total_ops
        
        print(f"{iterations:15d} | {total_ops:16d} | {relative_perf:17.1f}% | {description}")

if __name__ == "__main__":
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Test 1: Sqrt convergence for different rotation magnitudes
    test_exp_map_convergence()
    
    # Test 2: Specific test vectors (common SLAM scenarios)
    print("\n" + "="*80)
    print("SLAM-Specific Test Vectors")
    print("="*80)
    
    slam_test_vectors = [
        np.array([0.1, 0.05, 0.02]),      # Small rotation (typical odometry)
        np.array([0.5, 0.3, 0.1]),       # Medium rotation (turning)
        np.array([1.0, 0.8, 0.5]),       # Large rotation (loop closure)
        np.array([0.01, 0.01, 0.01]),    # Very small rotation (noise level)
        np.array([2.0, 1.5, 1.0]),       # Very large rotation (recovery)
    ]
    
    test_exp_map_accuracy(slam_test_vectors, iterations_list=[3, 5, 10])
    
    # Test 3: Performance analysis
    performance_impact_analysis()
    
    print("\n" + "="*80)
    print("RECOMMENDATIONS")
    print("="*80)
    print("Based on SLAM applications:")
    print("• 3 iterations: Good for most SLAM scenarios, 40% performance gain")
    print("• 5 iterations: Current setting, balanced accuracy/performance") 
    print("• 10 iterations: Overkill for SLAM, unnecessary precision")
    print("\nSuggested: Reduce Expmapping sqrt iterations from 10 to 3 or 5")
    print("Expected performance improvement: 20-40% for Expmapping operations")
