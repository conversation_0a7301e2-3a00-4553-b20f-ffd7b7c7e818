accuracy_test/rram_write_test.py:60: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at ../aten/src/ATen/native/ReduceOps.cpp:1808.)
  print(f"Ideal weights: mean={self.ideal_weights.mean().item():.6f}, "
Successfully imported all required modules.
Configuration: 100000 cycles, 1 updates/cycle, 1 cells
--Total cells: 1, D2D variation: 0.01, C2C variation: 0.003, ADC: 5 bits
Initializing weights with scale: 0.1
Ideal weights: mean=0.005877, std=nan, range=[0.006, 0.006]
Starting simulation: 100000 cycles * 1 updates per cycle = 100000 total updates

RRAM updates:   0%|          | 0/100000 [00:00<?, ?it/s]
RRAM updates:   0%|          | 175/100000 [00:00<00:57, 1747.78it/s]
RRAM updates:   0%|          | 352/100000 [00:00<00:56, 1757.54it/s]
RRAM updates:   1%|          | 529/100000 [00:00<00:56, 1760.95it/s]
RRAM updates:   1%|          | 709/100000 [00:00<00:55, 1774.26it/s]
RRAM updates:   1%|          | 888/100000 [00:00<00:55, 1779.81it/s]accuracy_test/rram_write_test.py:151: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at ../aten/src/ATen/native/ReduceOps.cpp:1808.)
  'rram_weight_std': self.rram_weights.std().item(),

RRAM updates:   1%|          | 1066/100000 [00:00<00:55, 1773.25it/s]
RRAM updates:   1%|          | 1244/100000 [00:00<00:55, 1772.03it/s]
RRAM updates:   1%|▏         | 1423/100000 [00:00<00:55, 1774.79it/s]
RRAM updates:   2%|▏         | 1601/100000 [00:00<00:55, 1775.60it/s]
RRAM updates:   2%|▏         | 1779/100000 [00:01<00:55, 1776.37it/s]
RRAM updates:   2%|▏         | 1957/100000 [00:01<00:55, 1776.87it/s]
RRAM updates:   2%|▏         | 2136/100000 [00:01<00:54, 1780.37it/s]
RRAM updates:   2%|▏         | 2317/100000 [00:01<00:54, 1786.66it/s]
RRAM updates:   2%|▏         | 2497/100000 [00:01<00:54, 1788.88it/s]
RRAM updates:   3%|▎         | 2676/100000 [00:01<00:54, 1779.09it/s]
RRAM updates:   3%|▎         | 2856/100000 [00:01<00:54, 1784.91it/s]
RRAM updates:   3%|▎         | 3036/100000 [00:01<00:54, 1788.75it/s]
RRAM updates:   3%|▎         | 3217/100000 [00:01<00:53, 1794.02it/s]
RRAM updates:   3%|▎         | 3397/100000 [00:01<00:53, 1795.67it/s]
RRAM updates:   4%|▎         | 3578/100000 [00:02<00:53, 1797.39it/s]
RRAM updates:   4%|▍         | 3759/100000 [00:02<00:53, 1799.59it/s]
RRAM updates:   4%|▍         | 3939/100000 [00:02<00:53, 1798.65it/s]
RRAM updates:   4%|▍         | 4119/100000 [00:02<00:53, 1796.37it/s]
RRAM updates:   4%|▍         | 4300/100000 [00:02<00:53, 1799.54it/s]
RRAM updates:   4%|▍         | 4481/100000 [00:02<00:53, 1800.79it/s]
RRAM updates:   5%|▍         | 4662/100000 [00:02<00:53, 1789.41it/s]
RRAM updates:   5%|▍         | 4842/100000 [00:02<00:53, 1792.17it/s]
RRAM updates:   5%|▌         | 5022/100000 [00:02<00:52, 1794.05it/s]
RRAM updates:   5%|▌         | 5202/100000 [00:02<00:52, 1795.37it/s]
RRAM updates:   5%|▌         | 5382/100000 [00:03<00:52, 1796.04it/s]
RRAM updates:   6%|▌         | 5563/100000 [00:03<00:52, 1797.61it/s]
RRAM updates:   6%|▌         | 5744/100000 [00:03<00:52, 1799.29it/s]
RRAM updates:   6%|▌         | 5926/100000 [00:03<00:52, 1803.63it/s]
RRAM updates:   6%|▌         | 6107/100000 [00:03<00:52, 1802.93it/s]
RRAM updates:   6%|▋         | 6288/100000 [00:03<00:52, 1793.00it/s]
RRAM updates:   6%|▋         | 6469/100000 [00:03<00:52, 1796.69it/s]
RRAM updates:   7%|▋         | 6651/100000 [00:03<00:51, 1802.39it/s]
RRAM updates:   7%|▋         | 6832/100000 [00:03<00:51, 1801.53it/s]
RRAM updates:   7%|▋         | 7013/100000 [00:03<00:51, 1796.72it/s]
RRAM updates:   7%|▋         | 7193/100000 [00:04<00:51, 1796.73it/s]
RRAM updates:   7%|▋         | 7374/100000 [00:04<00:51, 1800.13it/s]
RRAM updates:   8%|▊         | 7555/100000 [00:04<00:51, 1784.84it/s]
RRAM updates:   8%|▊         | 7736/100000 [00:04<00:51, 1790.60it/s]
RRAM updates:   8%|▊         | 7917/100000 [00:04<00:51, 1793.75it/s]
RRAM updates:   8%|▊         | 8097/100000 [00:04<00:51, 1793.82it/s]
RRAM updates:   8%|▊         | 8277/100000 [00:04<00:51, 1783.08it/s]
RRAM updates:   8%|▊         | 8458/100000 [00:04<00:51, 1789.87it/s]
RRAM updates:   9%|▊         | 8639/100000 [00:04<00:50, 1793.82it/s]
RRAM updates:   9%|▉         | 8820/100000 [00:04<00:50, 1797.33it/s]
RRAM updates:   9%|▉         | 9001/100000 [00:05<00:50, 1798.27it/s]
RRAM updates:   9%|▉         | 9181/100000 [00:05<00:50, 1798.69it/s]
RRAM updates:   9%|▉         | 9361/100000 [00:05<00:50, 1798.14it/s]
RRAM updates:  10%|▉         | 9541/100000 [00:05<00:50, 1797.61it/s]
RRAM updates:  10%|▉         | 9721/100000 [00:05<00:50, 1797.44it/s]
RRAM updates:  10%|▉         | 9901/100000 [00:05<00:50, 1795.80it/s]
RRAM updates:  10%|█         | 10081/100000 [00:05<00:50, 1795.01it/s]
RRAM updates:  10%|█         | 10262/100000 [00:05<00:49, 1797.27it/s]
RRAM updates:  10%|█         | 10443/100000 [00:05<00:49, 1799.27it/s]
RRAM updates:  11%|█         | 10625/100000 [00:05<00:49, 1802.65it/s]
RRAM updates:  11%|█         | 10806/100000 [00:06<00:49, 1791.48it/s]
RRAM updates:  11%|█         | 10987/100000 [00:06<00:49, 1795.67it/s]
RRAM updates:  11%|█         | 11167/100000 [00:06<00:49, 1792.73it/s]
RRAM updates:  11%|█▏        | 11347/100000 [00:06<00:49, 1794.40it/s]
RRAM updates:  12%|█▏        | 11528/100000 [00:06<00:49, 1797.16it/s]
RRAM updates:  12%|█▏        | 11708/100000 [00:06<00:49, 1795.60it/s]
RRAM updates:  12%|█▏        | 11888/100000 [00:06<00:49, 1796.03it/s]
RRAM updates:  12%|█▏        | 12069/100000 [00:06<00:48, 1797.50it/s]
RRAM updates:  12%|█▏        | 12251/100000 [00:06<00:48, 1802.16it/s]
RRAM updates:  12%|█▏        | 12432/100000 [00:06<00:48, 1801.28it/s]
RRAM updates:  13%|█▎        | 12613/100000 [00:07<00:48, 1801.60it/s]
RRAM updates:  13%|█▎        | 12794/100000 [00:07<00:48, 1799.16it/s]
RRAM updates:  13%|█▎        | 12975/100000 [00:07<00:48, 1800.11it/s]
RRAM updates:  13%|█▎        | 13156/100000 [00:07<00:48, 1801.95it/s]
RRAM updates:  13%|█▎        | 13337/100000 [00:07<00:48, 1803.65it/s]
RRAM updates:  14%|█▎        | 13518/100000 [00:07<00:47, 1804.36it/s]
RRAM updates:  14%|█▎        | 13699/100000 [00:07<00:47, 1802.44it/s]
RRAM updates:  14%|█▍        | 13880/100000 [00:07<00:47, 1798.93it/s]
RRAM updates:  14%|█▍        | 14060/100000 [00:07<00:47, 1797.73it/s]
RRAM updates:  14%|█▍        | 14240/100000 [00:07<00:47, 1786.85it/s]
RRAM updates:  14%|█▍        | 14420/100000 [00:08<00:47, 1789.54it/s]
RRAM updates:  15%|█▍        | 14600/100000 [00:08<00:47, 1791.32it/s]
RRAM updates:  15%|█▍        | 14780/100000 [00:08<00:47, 1792.79it/s]
RRAM updates:  15%|█▍        | 14960/100000 [00:08<00:47, 1792.70it/s]
RRAM updates:  15%|█▌        | 15140/100000 [00:08<00:47, 1790.39it/s]
RRAM updates:  15%|█▌        | 15320/100000 [00:08<00:47, 1790.32it/s]
RRAM updates:  16%|█▌        | 15500/100000 [00:08<00:47, 1792.93it/s]
RRAM updates:  16%|█▌        | 15680/100000 [00:08<00:46, 1795.03it/s]
RRAM updates:  16%|█▌        | 15861/100000 [00:08<00:46, 1796.82it/s]
RRAM updates:  16%|█▌        | 16041/100000 [00:08<00:46, 1795.04it/s]
RRAM updates:  16%|█▌        | 16221/100000 [00:09<00:46, 1796.39it/s]
RRAM updates:  16%|█▋        | 16401/100000 [00:09<00:46, 1797.17it/s]
RRAM updates:  17%|█▋        | 16581/100000 [00:09<00:46, 1796.36it/s]
RRAM updates:  17%|█▋        | 16762/100000 [00:09<00:46, 1798.02it/s]
RRAM updates:  17%|█▋        | 16944/100000 [00:09<00:46, 1801.95it/s]
RRAM updates:  17%|█▋        | 17125/100000 [00:09<00:45, 1803.48it/s]
RRAM updates:  17%|█▋        | 17306/100000 [00:09<00:45, 1803.28it/s]
RRAM updates:  17%|█▋        | 17487/100000 [00:09<00:45, 1803.26it/s]
RRAM updates:  18%|█▊        | 17668/100000 [00:09<00:45, 1804.08it/s]
RRAM updates:  18%|█▊        | 17849/100000 [00:09<00:47, 1723.95it/s]
RRAM updates:  18%|█▊        | 18028/100000 [00:10<00:47, 1740.53it/s]
RRAM updates:  18%|█▊        | 18209/100000 [00:10<00:46, 1758.50it/s]
RRAM updates:  18%|█▊        | 18389/100000 [00:10<00:46, 1769.97it/s]
RRAM updates:  19%|█▊        | 18570/100000 [00:10<00:45, 1780.35it/s]
RRAM updates:  19%|█▉        | 18750/100000 [00:10<00:45, 1784.06it/s]
RRAM updates:  19%|█▉        | 18931/100000 [00:10<00:45, 1789.35it/s]
RRAM updates:  19%|█▉        | 19111/100000 [00:10<00:45, 1788.65it/s]
RRAM updates:  19%|█▉        | 19291/100000 [00:10<00:45, 1790.88it/s]
RRAM updates:  19%|█▉        | 19471/100000 [00:10<00:44, 1792.60it/s]
RRAM updates:  20%|█▉        | 19651/100000 [00:10<00:44, 1794.52it/s]
RRAM updates:  20%|█▉        | 19832/100000 [00:11<00:44, 1799.00it/s]
RRAM updates:  20%|██        | 20013/100000 [00:11<00:44, 1800.69it/s]
RRAM updates:  20%|██        | 20194/100000 [00:11<00:44, 1800.65it/s]
RRAM updates:  20%|██        | 20375/100000 [00:11<00:44, 1801.74it/s]
RRAM updates:  21%|██        | 20556/100000 [00:11<00:44, 1797.92it/s]
RRAM updates:  21%|██        | 20737/100000 [00:11<00:44, 1799.20it/s]
RRAM updates:  21%|██        | 20918/100000 [00:11<00:43, 1799.68it/s]
RRAM updates:  21%|██        | 21098/100000 [00:11<00:43, 1798.38it/s]
RRAM updates:  21%|██▏       | 21278/100000 [00:11<00:43, 1797.47it/s]
RRAM updates:  21%|██▏       | 21458/100000 [00:11<00:43, 1795.58it/s]
RRAM updates:  22%|██▏       | 21638/100000 [00:12<00:43, 1794.83it/s]
RRAM updates:  22%|██▏       | 21818/100000 [00:12<00:43, 1794.44it/s]
RRAM updates:  22%|██▏       | 21999/100000 [00:12<00:43, 1797.19it/s]
RRAM updates:  22%|██▏       | 22180/100000 [00:12<00:43, 1798.08it/s]
RRAM updates:  22%|██▏       | 22360/100000 [00:12<00:43, 1796.00it/s]
RRAM updates:  23%|██▎       | 22540/100000 [00:12<00:43, 1795.48it/s]
RRAM updates:  23%|██▎       | 22720/100000 [00:12<00:43, 1794.21it/s]
RRAM updates:  23%|██▎       | 22900/100000 [00:12<00:43, 1783.07it/s]
RRAM updates:  23%|██▎       | 23079/100000 [00:12<00:43, 1784.15it/s]
RRAM updates:  23%|██▎       | 23260/100000 [00:12<00:42, 1789.03it/s]
RRAM updates:  23%|██▎       | 23440/100000 [00:13<00:42, 1791.19it/s]
RRAM updates:  24%|██▎       | 23620/100000 [00:13<00:42, 1792.26it/s]
RRAM updates:  24%|██▍       | 23801/100000 [00:13<00:42, 1795.33it/s]
RRAM updates:  24%|██▍       | 23981/100000 [00:13<00:42, 1796.46it/s]
RRAM updates:  24%|██▍       | 24161/100000 [00:13<00:42, 1795.45it/s]
RRAM updates:  24%|██▍       | 24341/100000 [00:13<00:42, 1793.36it/s]
RRAM updates:  25%|██▍       | 24521/100000 [00:13<00:42, 1793.97it/s]
RRAM updates:  25%|██▍       | 24701/100000 [00:13<00:41, 1795.77it/s]
RRAM updates:  25%|██▍       | 24881/100000 [00:13<00:41, 1795.82it/s]
RRAM updates:  25%|██▌       | 25061/100000 [00:13<00:41, 1793.04it/s]
RRAM updates:  25%|██▌       | 25241/100000 [00:14<00:41, 1793.75it/s]
RRAM updates:  25%|██▌       | 25421/100000 [00:14<00:44, 1666.55it/s]
RRAM updates:  26%|██▌       | 25603/100000 [00:14<00:43, 1707.61it/s]
RRAM updates:  26%|██▌       | 25784/100000 [00:14<00:42, 1736.48it/s]
RRAM updates:  26%|██▌       | 25965/100000 [00:14<00:42, 1757.04it/s]
RRAM updates:  26%|██▌       | 26147/100000 [00:14<00:41, 1772.83it/s]
RRAM updates:  26%|██▋       | 26329/100000 [00:14<00:41, 1784.30it/s]
RRAM updates:  27%|██▋       | 26510/100000 [00:14<00:41, 1789.18it/s]
RRAM updates:  27%|██▋       | 26691/100000 [00:14<00:40, 1794.91it/s]
RRAM updates:  27%|██▋       | 26872/100000 [00:15<00:40, 1797.70it/s]
RRAM updates:  27%|██▋       | 27053/100000 [00:15<00:40, 1799.12it/s]
RRAM updates:  27%|██▋       | 27234/100000 [00:15<00:40, 1801.33it/s]
RRAM updates:  27%|██▋       | 27415/100000 [00:15<00:40, 1802.31it/s]
RRAM updates:  28%|██▊       | 27596/100000 [00:15<00:40, 1802.68it/s]
RRAM updates:  28%|██▊       | 27777/100000 [00:15<00:40, 1804.43it/s]
RRAM updates:  28%|██▊       | 27958/100000 [00:15<00:39, 1804.28it/s]
RRAM updates:  28%|██▊       | 28139/100000 [00:15<00:39, 1804.64it/s]
RRAM updates:  28%|██▊       | 28321/100000 [00:15<00:39, 1807.31it/s]
RRAM updates:  29%|██▊       | 28502/100000 [00:15<00:39, 1796.92it/s]
RRAM updates:  29%|██▊       | 28683/100000 [00:16<00:39, 1799.77it/s]
RRAM updates:  29%|██▉       | 28864/100000 [00:16<00:39, 1800.67it/s]
RRAM updates:  29%|██▉       | 29045/100000 [00:16<00:39, 1800.89it/s]
RRAM updates:  29%|██▉       | 29226/100000 [00:16<00:39, 1800.82it/s]
RRAM updates:  29%|██▉       | 29407/100000 [00:16<00:39, 1800.47it/s]
RRAM updates:  30%|██▉       | 29589/100000 [00:16<00:39, 1803.41it/s]
RRAM updates:  30%|██▉       | 29770/100000 [00:16<00:38, 1803.52it/s]
RRAM updates:  30%|██▉       | 29951/100000 [00:16<00:38, 1803.07it/s]
RRAM updates:  30%|███       | 30132/100000 [00:16<00:38, 1803.51it/s]
RRAM updates:  30%|███       | 30313/100000 [00:16<00:38, 1803.27it/s]
RRAM updates:  30%|███       | 30494/100000 [00:17<00:38, 1804.25it/s]
RRAM updates:  31%|███       | 30675/100000 [00:17<00:38, 1805.76it/s]
RRAM updates:  31%|███       | 30857/100000 [00:17<00:38, 1807.82it/s]
RRAM updates:  31%|███       | 31039/100000 [00:17<00:38, 1809.91it/s]
RRAM updates:  31%|███       | 31221/100000 [00:17<00:37, 1811.65it/s]
RRAM updates:  31%|███▏      | 31403/100000 [00:17<00:37, 1810.04it/s]
RRAM updates:  32%|███▏      | 31585/100000 [00:17<00:37, 1806.36it/s]
RRAM updates:  32%|███▏      | 31766/100000 [00:17<00:38, 1762.36it/s]
RRAM updates:  32%|███▏      | 31947/100000 [00:17<00:38, 1774.38it/s]
RRAM updates:  32%|███▏      | 32128/100000 [00:17<00:38, 1784.37it/s]
RRAM updates:  32%|███▏      | 32310/100000 [00:18<00:37, 1793.92it/s]
RRAM updates:  32%|███▏      | 32492/100000 [00:18<00:37, 1799.35it/s]
RRAM updates:  33%|███▎      | 32674/100000 [00:18<00:37, 1803.30it/s]
RRAM updates:  33%|███▎      | 32856/100000 [00:18<00:37, 1805.36it/s]
RRAM updates:  33%|███▎      | 33037/100000 [00:18<00:37, 1804.28it/s]
RRAM updates:  33%|███▎      | 33218/100000 [00:18<00:37, 1804.31it/s]
RRAM updates:  33%|███▎      | 33399/100000 [00:18<00:36, 1804.85it/s]
RRAM updates:  34%|███▎      | 33580/100000 [00:18<00:36, 1805.67it/s]
RRAM updates:  34%|███▍      | 33762/100000 [00:18<00:36, 1808.22it/s]
RRAM updates:  34%|███▍      | 33944/100000 [00:18<00:36, 1810.59it/s]
RRAM updates:  34%|███▍      | 34126/100000 [00:19<00:36, 1810.36it/s]
RRAM updates:  34%|███▍      | 34308/100000 [00:19<00:36, 1811.80it/s]
RRAM updates:  34%|███▍      | 34490/100000 [00:19<00:36, 1810.53it/s]
RRAM updates:  35%|███▍      | 34672/100000 [00:19<00:36, 1806.48it/s]
RRAM updates:  35%|███▍      | 34853/100000 [00:19<00:36, 1806.38it/s]
RRAM updates:  35%|███▌      | 35034/100000 [00:19<00:36, 1804.09it/s]
RRAM updates:  35%|███▌      | 35215/100000 [00:19<00:35, 1805.24it/s]
RRAM updates:  35%|███▌      | 35397/100000 [00:19<00:35, 1807.63it/s]
RRAM updates:  36%|███▌      | 35578/100000 [00:19<00:35, 1808.12it/s]
RRAM updates:  36%|███▌      | 35760/100000 [00:19<00:35, 1809.29it/s]
RRAM updates:  36%|███▌      | 35941/100000 [00:20<00:35, 1796.31it/s]
RRAM updates:  36%|███▌      | 36122/100000 [00:20<00:35, 1800.15it/s]
RRAM updates:  36%|███▋      | 36304/100000 [00:20<00:35, 1805.44it/s]
RRAM updates:  36%|███▋      | 36486/100000 [00:20<00:35, 1807.00it/s]
RRAM updates:  37%|███▋      | 36668/100000 [00:20<00:35, 1808.23it/s]
RRAM updates:  37%|███▋      | 36849/100000 [00:20<00:34, 1807.60it/s]
RRAM updates:  37%|███▋      | 37030/100000 [00:20<00:35, 1763.34it/s]
RRAM updates:  37%|███▋      | 37210/100000 [00:20<00:35, 1772.05it/s]
RRAM updates:  37%|███▋      | 37389/100000 [00:20<00:35, 1777.16it/s]
RRAM updates:  38%|███▊      | 37569/100000 [00:20<00:35, 1781.37it/s]
RRAM updates:  38%|███▊      | 37750/100000 [00:21<00:34, 1788.12it/s]
RRAM updates:  38%|███▊      | 37929/100000 [00:21<00:34, 1787.36it/s]
RRAM updates:  38%|███▊      | 38109/100000 [00:21<00:34, 1789.98it/s]
RRAM updates:  38%|███▊      | 38290/100000 [00:21<00:34, 1793.00it/s]
RRAM updates:  38%|███▊      | 38471/100000 [00:21<00:34, 1795.54it/s]
RRAM updates:  39%|███▊      | 38651/100000 [00:21<00:34, 1796.72it/s]
RRAM updates:  39%|███▉      | 38831/100000 [00:21<00:34, 1797.16it/s]
RRAM updates:  39%|███▉      | 39011/100000 [00:21<00:33, 1796.81it/s]
RRAM updates:  39%|███▉      | 39191/100000 [00:21<00:33, 1797.67it/s]
RRAM updates:  39%|███▉      | 39371/100000 [00:21<00:33, 1797.47it/s]
RRAM updates:  40%|███▉      | 39551/100000 [00:22<00:33, 1795.45it/s]
RRAM updates:  40%|███▉      | 39731/100000 [00:22<00:33, 1796.48it/s]
RRAM updates:  40%|███▉      | 39911/100000 [00:22<00:33, 1796.54it/s]
RRAM updates:  40%|████      | 40091/100000 [00:22<00:33, 1794.86it/s]
RRAM updates:  40%|████      | 40272/100000 [00:22<00:33, 1798.88it/s]
RRAM updates:  40%|████      | 40453/100000 [00:22<00:33, 1799.43it/s]
RRAM updates:  41%|████      | 40634/100000 [00:22<00:32, 1799.98it/s]
RRAM updates:  41%|████      | 40815/100000 [00:22<00:32, 1800.33it/s]
RRAM updates:  41%|████      | 40996/100000 [00:22<00:32, 1799.08it/s]
RRAM updates:  41%|████      | 41177/100000 [00:22<00:32, 1802.01it/s]
RRAM updates:  41%|████▏     | 41358/100000 [00:23<00:32, 1801.45it/s]
RRAM updates:  42%|████▏     | 41539/100000 [00:23<00:32, 1801.45it/s]
RRAM updates:  42%|████▏     | 41720/100000 [00:23<00:32, 1799.34it/s]
RRAM updates:  42%|████▏     | 41901/100000 [00:23<00:32, 1801.41it/s]
RRAM updates:  42%|████▏     | 42082/100000 [00:23<00:32, 1801.46it/s]
RRAM updates:  42%|████▏     | 42263/100000 [00:23<00:32, 1802.25it/s]
RRAM updates:  42%|████▏     | 42444/100000 [00:23<00:31, 1800.99it/s]
RRAM updates:  43%|████▎     | 42625/100000 [00:23<00:31, 1799.21it/s]
RRAM updates:  43%|████▎     | 42805/100000 [00:23<00:31, 1799.05it/s]
RRAM updates:  43%|████▎     | 42985/100000 [00:23<00:31, 1796.81it/s]
RRAM updates:  43%|████▎     | 43166/100000 [00:24<00:31, 1797.85it/s]
RRAM updates:  43%|████▎     | 43346/100000 [00:24<00:31, 1797.98it/s]
RRAM updates:  44%|████▎     | 43526/100000 [00:24<00:31, 1797.66it/s]
RRAM updates:  44%|████▎     | 43707/100000 [00:24<00:31, 1799.24it/s]
RRAM updates:  44%|████▍     | 43887/100000 [00:24<00:31, 1797.68it/s]
RRAM updates:  44%|████▍     | 44067/100000 [00:24<00:31, 1794.73it/s]
RRAM updates:  44%|████▍     | 44247/100000 [00:24<00:31, 1794.63it/s]
RRAM updates:  44%|████▍     | 44428/100000 [00:24<00:30, 1796.29it/s]
RRAM updates:  45%|████▍     | 44609/100000 [00:24<00:30, 1797.46it/s]
RRAM updates:  45%|████▍     | 44790/100000 [00:24<00:30, 1800.08it/s]
RRAM updates:  45%|████▍     | 44971/100000 [00:25<00:30, 1800.41it/s]
RRAM updates:  45%|████▌     | 45152/100000 [00:25<00:30, 1800.66it/s]
RRAM updates:  45%|████▌     | 45333/100000 [00:25<00:31, 1756.54it/s]
RRAM updates:  46%|████▌     | 45509/100000 [00:25<00:31, 1756.34it/s]
RRAM updates:  46%|████▌     | 45690/100000 [00:25<00:30, 1769.33it/s]
RRAM updates:  46%|████▌     | 45871/100000 [00:25<00:30, 1780.19it/s]
RRAM updates:  46%|████▌     | 46052/100000 [00:25<00:30, 1786.72it/s]
RRAM updates:  46%|████▌     | 46232/100000 [00:25<00:30, 1789.31it/s]
RRAM updates:  46%|████▋     | 46413/100000 [00:25<00:29, 1793.31it/s]
RRAM updates:  47%|████▋     | 46594/100000 [00:25<00:29, 1797.14it/s]
RRAM updates:  47%|████▋     | 46775/100000 [00:26<00:29, 1798.19it/s]
RRAM updates:  47%|████▋     | 46955/100000 [00:26<00:29, 1795.42it/s]
RRAM updates:  47%|████▋     | 47135/100000 [00:26<00:29, 1796.29it/s]
RRAM updates:  47%|████▋     | 47315/100000 [00:26<00:29, 1794.19it/s]
RRAM updates:  47%|████▋     | 47495/100000 [00:26<00:29, 1794.68it/s]
RRAM updates:  48%|████▊     | 47675/100000 [00:26<00:29, 1794.72it/s]
RRAM updates:  48%|████▊     | 47855/100000 [00:26<00:30, 1697.75it/s]
RRAM updates:  48%|████▊     | 48033/100000 [00:26<00:30, 1719.02it/s]
RRAM updates:  48%|████▊     | 48210/100000 [00:26<00:29, 1732.50it/s]
RRAM updates:  48%|████▊     | 48388/100000 [00:27<00:29, 1746.01it/s]
RRAM updates:  49%|████▊     | 48567/100000 [00:27<00:29, 1756.19it/s]
RRAM updates:  49%|████▊     | 48745/100000 [00:27<00:29, 1762.93it/s]
RRAM updates:  49%|████▉     | 48924/100000 [00:27<00:28, 1768.36it/s]
RRAM updates:  49%|████▉     | 49102/100000 [00:27<00:28, 1770.69it/s]
RRAM updates:  49%|████▉     | 49280/100000 [00:27<00:28, 1772.70it/s]
RRAM updates:  49%|████▉     | 49459/100000 [00:27<00:28, 1776.86it/s]
RRAM updates:  50%|████▉     | 49638/100000 [00:27<00:28, 1778.05it/s]
RRAM updates:  50%|████▉     | 49817/100000 [00:27<00:28, 1778.57it/s]
RRAM updates:  50%|████▉     | 49996/100000 [00:27<00:28, 1781.95it/s]
RRAM updates:  50%|█████     | 50175/100000 [00:28<00:27, 1780.45it/s]
RRAM updates:  50%|█████     | 50354/100000 [00:28<00:27, 1781.70it/s]
RRAM updates:  51%|█████     | 50533/100000 [00:28<00:27, 1782.37it/s]
RRAM updates:  51%|█████     | 50712/100000 [00:28<00:27, 1779.96it/s]
RRAM updates:  51%|█████     | 50891/100000 [00:28<00:27, 1779.28it/s]
RRAM updates:  51%|█████     | 51069/100000 [00:28<00:27, 1778.68it/s]
RRAM updates:  51%|█████     | 51247/100000 [00:28<00:27, 1778.56it/s]
RRAM updates:  51%|█████▏    | 51426/100000 [00:28<00:27, 1781.58it/s]
RRAM updates:  52%|█████▏    | 51605/100000 [00:28<00:27, 1783.80it/s]
RRAM updates:  52%|█████▏    | 51784/100000 [00:28<00:27, 1783.73it/s]
RRAM updates:  52%|█████▏    | 51964/100000 [00:29<00:26, 1786.34it/s]
RRAM updates:  52%|█████▏    | 52144/100000 [00:29<00:26, 1788.20it/s]
RRAM updates:  52%|█████▏    | 52323/100000 [00:29<00:26, 1788.35it/s]
RRAM updates:  53%|█████▎    | 52502/100000 [00:29<00:26, 1783.63it/s]
RRAM updates:  53%|█████▎    | 52681/100000 [00:29<00:26, 1781.27it/s]
RRAM updates:  53%|█████▎    | 52860/100000 [00:29<00:26, 1779.24it/s]
RRAM updates:  53%|█████▎    | 53039/100000 [00:29<00:26, 1780.41it/s]
RRAM updates:  53%|█████▎    | 53218/100000 [00:29<00:26, 1783.20it/s]
RRAM updates:  53%|█████▎    | 53397/100000 [00:29<00:26, 1782.36it/s]
RRAM updates:  54%|█████▎    | 53576/100000 [00:29<00:26, 1782.13it/s]
RRAM updates:  54%|█████▍    | 53755/100000 [00:30<00:25, 1779.83it/s]
RRAM updates:  54%|█████▍    | 53934/100000 [00:30<00:25, 1782.12it/s]
RRAM updates:  54%|█████▍    | 54113/100000 [00:30<00:25, 1780.27it/s]
RRAM updates:  54%|█████▍    | 54292/100000 [00:30<00:25, 1781.61it/s]
RRAM updates:  54%|█████▍    | 54471/100000 [00:30<00:25, 1781.46it/s]
RRAM updates:  55%|█████▍    | 54650/100000 [00:30<00:25, 1781.18it/s]
RRAM updates:  55%|█████▍    | 54829/100000 [00:30<00:25, 1783.05it/s]
RRAM updates:  55%|█████▌    | 55008/100000 [00:30<00:25, 1780.47it/s]
RRAM updates:  55%|█████▌    | 55187/100000 [00:30<00:26, 1691.80it/s]
RRAM updates:  55%|█████▌    | 55365/100000 [00:30<00:26, 1715.90it/s]
RRAM updates:  56%|█████▌    | 55545/100000 [00:31<00:25, 1738.12it/s]
RRAM updates:  56%|█████▌    | 55726/100000 [00:31<00:25, 1756.57it/s]
RRAM updates:  56%|█████▌    | 55907/100000 [00:31<00:24, 1770.19it/s]
RRAM updates:  56%|█████▌    | 56087/100000 [00:31<00:24, 1777.34it/s]
RRAM updates:  56%|█████▋    | 56268/100000 [00:31<00:24, 1785.77it/s]
RRAM updates:  56%|█████▋    | 56449/100000 [00:31<00:24, 1792.87it/s]
RRAM updates:  57%|█████▋    | 56629/100000 [00:31<00:24, 1794.47it/s]
RRAM updates:  57%|█████▋    | 56809/100000 [00:31<00:24, 1794.65it/s]
RRAM updates:  57%|█████▋    | 56989/100000 [00:31<00:23, 1794.44it/s]
RRAM updates:  57%|█████▋    | 57169/100000 [00:31<00:23, 1788.01it/s]
RRAM updates:  57%|█████▋    | 57349/100000 [00:32<00:23, 1790.56it/s]
RRAM updates:  58%|█████▊    | 57529/100000 [00:32<00:23, 1791.85it/s]
RRAM updates:  58%|█████▊    | 57709/100000 [00:32<00:23, 1784.50it/s]
RRAM updates:  58%|█████▊    | 57889/100000 [00:32<00:23, 1787.17it/s]
RRAM updates:  58%|█████▊    | 58068/100000 [00:32<00:23, 1784.17it/s]
RRAM updates:  58%|█████▊    | 58248/100000 [00:32<00:23, 1786.43it/s]
RRAM updates:  58%|█████▊    | 58429/100000 [00:32<00:23, 1791.83it/s]
RRAM updates:  59%|█████▊    | 58611/100000 [00:32<00:23, 1797.53it/s]
RRAM updates:  59%|█████▉    | 58791/100000 [00:32<00:22, 1794.30it/s]
RRAM updates:  59%|█████▉    | 58971/100000 [00:32<00:22, 1790.32it/s]
RRAM updates:  59%|█████▉    | 59151/100000 [00:33<00:22, 1788.09it/s]
RRAM updates:  59%|█████▉    | 59330/100000 [00:33<00:22, 1775.71it/s]
RRAM updates:  60%|█████▉    | 59509/100000 [00:33<00:22, 1777.72it/s]
RRAM updates:  60%|█████▉    | 59689/100000 [00:33<00:22, 1782.52it/s]
RRAM updates:  60%|█████▉    | 59869/100000 [00:33<00:22, 1785.37it/s]
RRAM updates:  60%|██████    | 60048/100000 [00:33<00:22, 1786.70it/s]
RRAM updates:  60%|██████    | 60228/100000 [00:33<00:22, 1789.56it/s]
RRAM updates:  60%|██████    | 60408/100000 [00:33<00:22, 1792.33it/s]
RRAM updates:  61%|██████    | 60588/100000 [00:33<00:21, 1792.92it/s]
RRAM updates:  61%|██████    | 60768/100000 [00:33<00:21, 1787.95it/s]
RRAM updates:  61%|██████    | 60947/100000 [00:34<00:21, 1783.76it/s]
RRAM updates:  61%|██████    | 61126/100000 [00:34<00:21, 1780.09it/s]
RRAM updates:  61%|██████▏   | 61305/100000 [00:34<00:21, 1781.43it/s]
RRAM updates:  61%|██████▏   | 61484/100000 [00:34<00:21, 1779.82it/s]
RRAM updates:  62%|██████▏   | 61664/100000 [00:34<00:21, 1784.03it/s]
RRAM updates:  62%|██████▏   | 61844/100000 [00:34<00:21, 1788.23it/s]
RRAM updates:  62%|██████▏   | 62025/100000 [00:34<00:21, 1793.65it/s]
RRAM updates:  62%|██████▏   | 62206/100000 [00:34<00:21, 1798.12it/s]
RRAM updates:  62%|██████▏   | 62388/100000 [00:34<00:20, 1801.77it/s]
RRAM updates:  63%|██████▎   | 62569/100000 [00:34<00:20, 1803.53it/s]
RRAM updates:  63%|██████▎   | 62750/100000 [00:35<00:20, 1780.80it/s]
RRAM updates:  63%|██████▎   | 62932/100000 [00:35<00:20, 1791.41it/s]
RRAM updates:  63%|██████▎   | 63113/100000 [00:35<00:20, 1795.98it/s]
RRAM updates:  63%|██████▎   | 63295/100000 [00:35<00:20, 1800.67it/s]
RRAM updates:  63%|██████▎   | 63476/100000 [00:35<00:20, 1802.12it/s]
RRAM updates:  64%|██████▎   | 63657/100000 [00:35<00:20, 1801.16it/s]
RRAM updates:  64%|██████▍   | 63838/100000 [00:35<00:20, 1802.16it/s]
RRAM updates:  64%|██████▍   | 64019/100000 [00:35<00:19, 1803.22it/s]
RRAM updates:  64%|██████▍   | 64201/100000 [00:35<00:19, 1805.40it/s]
RRAM updates:  64%|██████▍   | 64382/100000 [00:35<00:19, 1802.65it/s]
RRAM updates:  65%|██████▍   | 64563/100000 [00:36<00:19, 1804.44it/s]
RRAM updates:  65%|██████▍   | 64744/100000 [00:36<00:19, 1804.97it/s]
RRAM updates:  65%|██████▍   | 64925/100000 [00:36<00:19, 1805.18it/s]
RRAM updates:  65%|██████▌   | 65106/100000 [00:36<00:19, 1803.27it/s]
RRAM updates:  65%|██████▌   | 65287/100000 [00:36<00:19, 1800.57it/s]
RRAM updates:  65%|██████▌   | 65468/100000 [00:36<00:19, 1800.55it/s]
RRAM updates:  66%|██████▌   | 65650/100000 [00:36<00:19, 1803.46it/s]
RRAM updates:  66%|██████▌   | 65832/100000 [00:36<00:18, 1806.45it/s]
RRAM updates:  66%|██████▌   | 66013/100000 [00:36<00:18, 1806.30it/s]
RRAM updates:  66%|██████▌   | 66195/100000 [00:36<00:18, 1807.52it/s]
RRAM updates:  66%|██████▋   | 66377/100000 [00:37<00:18, 1808.50it/s]
RRAM updates:  67%|██████▋   | 66559/100000 [00:37<00:18, 1809.49it/s]
RRAM updates:  67%|██████▋   | 66741/100000 [00:37<00:18, 1809.72it/s]
RRAM updates:  67%|██████▋   | 66923/100000 [00:37<00:18, 1810.03it/s]
RRAM updates:  67%|██████▋   | 67105/100000 [00:37<00:18, 1810.89it/s]
RRAM updates:  67%|██████▋   | 67287/100000 [00:37<00:18, 1813.35it/s]
RRAM updates:  67%|██████▋   | 67469/100000 [00:37<00:17, 1813.98it/s]
RRAM updates:  68%|██████▊   | 67651/100000 [00:37<00:17, 1815.10it/s]
RRAM updates:  68%|██████▊   | 67834/100000 [00:37<00:17, 1816.73it/s]
RRAM updates:  68%|██████▊   | 68016/100000 [00:37<00:17, 1815.50it/s]
RRAM updates:  68%|██████▊   | 68198/100000 [00:38<00:17, 1815.57it/s]
RRAM updates:  68%|██████▊   | 68380/100000 [00:38<00:17, 1814.94it/s]
RRAM updates:  69%|██████▊   | 68562/100000 [00:38<00:17, 1811.25it/s]
RRAM updates:  69%|██████▊   | 68744/100000 [00:38<00:17, 1810.08it/s]
RRAM updates:  69%|██████▉   | 68926/100000 [00:38<00:17, 1808.43it/s]
RRAM updates:  69%|██████▉   | 69107/100000 [00:38<00:17, 1807.40it/s]
RRAM updates:  69%|██████▉   | 69288/100000 [00:38<00:16, 1806.69it/s]
RRAM updates:  69%|██████▉   | 69470/100000 [00:38<00:16, 1809.16it/s]
RRAM updates:  70%|██████▉   | 69652/100000 [00:38<00:16, 1809.53it/s]
RRAM updates:  70%|██████▉   | 69833/100000 [00:38<00:16, 1809.33it/s]
RRAM updates:  70%|███████   | 70014/100000 [00:39<00:16, 1809.15it/s]
RRAM updates:  70%|███████   | 70196/100000 [00:39<00:16, 1811.19it/s]
RRAM updates:  70%|███████   | 70378/100000 [00:39<00:16, 1811.89it/s]
RRAM updates:  71%|███████   | 70560/100000 [00:39<00:16, 1812.73it/s]
RRAM updates:  71%|███████   | 70742/100000 [00:39<00:16, 1814.84it/s]
RRAM updates:  71%|███████   | 70925/100000 [00:39<00:16, 1817.09it/s]
RRAM updates:  71%|███████   | 71107/100000 [00:39<00:16, 1774.00it/s]
RRAM updates:  71%|███████▏  | 71285/100000 [00:39<00:17, 1650.75it/s]
RRAM updates:  71%|███████▏  | 71466/100000 [00:39<00:16, 1694.02it/s]
RRAM updates:  72%|███████▏  | 71637/100000 [00:40<00:16, 1678.47it/s]
RRAM updates:  72%|███████▏  | 71809/100000 [00:40<00:16, 1689.21it/s]
RRAM updates:  72%|███████▏  | 71986/100000 [00:40<00:16, 1712.60it/s]
RRAM updates:  72%|███████▏  | 72165/100000 [00:40<00:16, 1732.95it/s]
RRAM updates:  72%|███████▏  | 72344/100000 [00:40<00:15, 1748.82it/s]
RRAM updates:  73%|███████▎  | 72522/100000 [00:40<00:15, 1756.13it/s]
RRAM updates:  73%|███████▎  | 72698/100000 [00:40<00:15, 1749.16it/s]
RRAM updates:  73%|███████▎  | 72876/100000 [00:40<00:15, 1756.19it/s]
RRAM updates:  73%|███████▎  | 73053/100000 [00:40<00:15, 1757.88it/s]
RRAM updates:  73%|███████▎  | 73231/100000 [00:40<00:15, 1761.73it/s]
RRAM updates:  73%|███████▎  | 73408/100000 [00:41<00:15, 1764.02it/s]
RRAM updates:  74%|███████▎  | 73586/100000 [00:41<00:14, 1766.77it/s]
RRAM updates:  74%|███████▍  | 73765/100000 [00:41<00:14, 1770.89it/s]
RRAM updates:  74%|███████▍  | 73944/100000 [00:41<00:14, 1773.77it/s]
RRAM updates:  74%|███████▍  | 74122/100000 [00:41<00:14, 1769.02it/s]
RRAM updates:  74%|███████▍  | 74300/100000 [00:41<00:14, 1769.31it/s]
RRAM updates:  74%|███████▍  | 74477/100000 [00:41<00:14, 1764.47it/s]
RRAM updates:  75%|███████▍  | 74655/100000 [00:41<00:14, 1769.08it/s]
RRAM updates:  75%|███████▍  | 74833/100000 [00:41<00:14, 1769.86it/s]
RRAM updates:  75%|███████▌  | 75011/100000 [00:41<00:14, 1770.05it/s]
RRAM updates:  75%|███████▌  | 75189/100000 [00:42<00:13, 1772.28it/s]
RRAM updates:  75%|███████▌  | 75368/100000 [00:42<00:13, 1774.94it/s]
RRAM updates:  76%|███████▌  | 75546/100000 [00:42<00:13, 1775.83it/s]
RRAM updates:  76%|███████▌  | 75725/100000 [00:42<00:13, 1777.64it/s]
RRAM updates:  76%|███████▌  | 75905/100000 [00:42<00:13, 1781.50it/s]
RRAM updates:  76%|███████▌  | 76085/100000 [00:42<00:13, 1784.23it/s]
RRAM updates:  76%|███████▋  | 76264/100000 [00:42<00:13, 1785.56it/s]
RRAM updates:  76%|███████▋  | 76443/100000 [00:42<00:13, 1785.88it/s]
RRAM updates:  77%|███████▋  | 76622/100000 [00:42<00:13, 1786.01it/s]
RRAM updates:  77%|███████▋  | 76801/100000 [00:42<00:12, 1786.75it/s]
RRAM updates:  77%|███████▋  | 76980/100000 [00:43<00:12, 1784.34it/s]
RRAM updates:  77%|███████▋  | 77159/100000 [00:43<00:12, 1783.15it/s]
RRAM updates:  77%|███████▋  | 77338/100000 [00:43<00:12, 1784.06it/s]
RRAM updates:  78%|███████▊  | 77517/100000 [00:43<00:12, 1779.50it/s]
RRAM updates:  78%|███████▊  | 77695/100000 [00:43<00:12, 1776.65it/s]
RRAM updates:  78%|███████▊  | 77873/100000 [00:43<00:12, 1772.79it/s]
RRAM updates:  78%|███████▊  | 78051/100000 [00:43<00:12, 1773.00it/s]
RRAM updates:  78%|███████▊  | 78230/100000 [00:43<00:12, 1777.41it/s]
RRAM updates:  78%|███████▊  | 78408/100000 [00:43<00:12, 1778.00it/s]
RRAM updates:  79%|███████▊  | 78586/100000 [00:43<00:12, 1772.84it/s]
RRAM updates:  79%|███████▉  | 78765/100000 [00:44<00:11, 1776.34it/s]
RRAM updates:  79%|███████▉  | 78944/100000 [00:44<00:11, 1778.42it/s]
RRAM updates:  79%|███████▉  | 79122/100000 [00:44<00:11, 1777.80it/s]
RRAM updates:  79%|███████▉  | 79301/100000 [00:44<00:11, 1780.79it/s]
RRAM updates:  79%|███████▉  | 79480/100000 [00:44<00:11, 1779.16it/s]
RRAM updates:  80%|███████▉  | 79659/100000 [00:44<00:11, 1781.12it/s]
RRAM updates:  80%|███████▉  | 79838/100000 [00:44<00:11, 1781.98it/s]
RRAM updates:  80%|████████  | 80017/100000 [00:44<00:11, 1783.20it/s]
RRAM updates:  80%|████████  | 80197/100000 [00:44<00:11, 1785.29it/s]
RRAM updates:  80%|████████  | 80377/100000 [00:44<00:10, 1787.14it/s]
RRAM updates:  81%|████████  | 80556/100000 [00:45<00:10, 1787.80it/s]
RRAM updates:  81%|████████  | 80735/100000 [00:45<00:10, 1788.35it/s]
RRAM updates:  81%|████████  | 80914/100000 [00:45<00:10, 1788.28it/s]
RRAM updates:  81%|████████  | 81093/100000 [00:45<00:10, 1785.38it/s]
RRAM updates:  81%|████████▏ | 81272/100000 [00:45<00:10, 1785.06it/s]
RRAM updates:  81%|████████▏ | 81451/100000 [00:45<00:10, 1782.77it/s]
RRAM updates:  82%|████████▏ | 81630/100000 [00:45<00:10, 1782.02it/s]
RRAM updates:  82%|████████▏ | 81809/100000 [00:45<00:10, 1675.01it/s]
RRAM updates:  82%|████████▏ | 81987/100000 [00:45<00:10, 1703.58it/s]
RRAM updates:  82%|████████▏ | 82165/100000 [00:45<00:10, 1725.29it/s]
RRAM updates:  82%|████████▏ | 82344/100000 [00:46<00:10, 1743.06it/s]
RRAM updates:  83%|████████▎ | 82523/100000 [00:46<00:09, 1755.11it/s]
RRAM updates:  83%|████████▎ | 82703/100000 [00:46<00:09, 1765.62it/s]
RRAM updates:  83%|████████▎ | 82882/100000 [00:46<00:09, 1772.45it/s]
RRAM updates:  83%|████████▎ | 83060/100000 [00:46<00:09, 1770.46it/s]
RRAM updates:  83%|████████▎ | 83238/100000 [00:46<00:09, 1771.42it/s]
RRAM updates:  83%|████████▎ | 83416/100000 [00:46<00:09, 1769.00it/s]
RRAM updates:  84%|████████▎ | 83595/100000 [00:46<00:09, 1772.50it/s]
RRAM updates:  84%|████████▍ | 83775/100000 [00:46<00:09, 1777.85it/s]
RRAM updates:  84%|████████▍ | 83953/100000 [00:46<00:09, 1777.63it/s]
RRAM updates:  84%|████████▍ | 84131/100000 [00:47<00:08, 1777.99it/s]
RRAM updates:  84%|████████▍ | 84310/100000 [00:47<00:08, 1780.38it/s]
RRAM updates:  84%|████████▍ | 84489/100000 [00:47<00:08, 1781.43it/s]
RRAM updates:  85%|████████▍ | 84668/100000 [00:47<00:08, 1778.85it/s]
RRAM updates:  85%|████████▍ | 84847/100000 [00:47<00:08, 1779.76it/s]
RRAM updates:  85%|████████▌ | 85026/100000 [00:47<00:08, 1780.93it/s]
RRAM updates:  85%|████████▌ | 85205/100000 [00:47<00:08, 1774.06it/s]
RRAM updates:  85%|████████▌ | 85383/100000 [00:47<00:08, 1769.25it/s]
RRAM updates:  86%|████████▌ | 85561/100000 [00:47<00:08, 1770.93it/s]
RRAM updates:  86%|████████▌ | 85739/100000 [00:47<00:08, 1772.21it/s]
RRAM updates:  86%|████████▌ | 85917/100000 [00:48<00:07, 1769.69it/s]
RRAM updates:  86%|████████▌ | 86094/100000 [00:48<00:07, 1763.20it/s]
RRAM updates:  86%|████████▋ | 86274/100000 [00:48<00:07, 1772.61it/s]
RRAM updates:  86%|████████▋ | 86454/100000 [00:48<00:07, 1779.69it/s]
RRAM updates:  87%|████████▋ | 86632/100000 [00:48<00:07, 1774.87it/s]
RRAM updates:  87%|████████▋ | 86810/100000 [00:48<00:07, 1775.28it/s]
RRAM updates:  87%|████████▋ | 86989/100000 [00:48<00:07, 1778.14it/s]
RRAM updates:  87%|████████▋ | 87168/100000 [00:48<00:07, 1780.17it/s]
RRAM updates:  87%|████████▋ | 87347/100000 [00:48<00:07, 1781.04it/s]
RRAM updates:  88%|████████▊ | 87526/100000 [00:48<00:07, 1780.51it/s]
RRAM updates:  88%|████████▊ | 87705/100000 [00:49<00:06, 1781.91it/s]
RRAM updates:  88%|████████▊ | 87884/100000 [00:49<00:06, 1783.65it/s]
RRAM updates:  88%|████████▊ | 88063/100000 [00:49<00:06, 1781.79it/s]
RRAM updates:  88%|████████▊ | 88242/100000 [00:49<00:06, 1782.26it/s]
RRAM updates:  88%|████████▊ | 88421/100000 [00:49<00:06, 1782.66it/s]
RRAM updates:  89%|████████▊ | 88600/100000 [00:49<00:06, 1779.10it/s]
RRAM updates:  89%|████████▉ | 88779/100000 [00:49<00:06, 1780.16it/s]
RRAM updates:  89%|████████▉ | 88958/100000 [00:49<00:06, 1783.02it/s]
RRAM updates:  89%|████████▉ | 89137/100000 [00:49<00:06, 1782.98it/s]
RRAM updates:  89%|████████▉ | 89316/100000 [00:49<00:05, 1784.48it/s]
RRAM updates:  89%|████████▉ | 89495/100000 [00:50<00:05, 1778.50it/s]
RRAM updates:  90%|████████▉ | 89673/100000 [00:50<00:05, 1773.95it/s]
RRAM updates:  90%|████████▉ | 89851/100000 [00:50<00:05, 1774.99it/s]
RRAM updates:  90%|█████████ | 90029/100000 [00:50<00:05, 1776.11it/s]
RRAM updates:  90%|█████████ | 90207/100000 [00:50<00:05, 1776.41it/s]
RRAM updates:  90%|█████████ | 90386/100000 [00:50<00:05, 1778.19it/s]
RRAM updates:  91%|█████████ | 90564/100000 [00:50<00:05, 1772.03it/s]
RRAM updates:  91%|█████████ | 90742/100000 [00:50<00:05, 1772.20it/s]
RRAM updates:  91%|█████████ | 90920/100000 [00:50<00:05, 1770.01it/s]
RRAM updates:  91%|█████████ | 91098/100000 [00:50<00:05, 1768.73it/s]
RRAM updates:  91%|█████████▏| 91275/100000 [00:51<00:04, 1767.60it/s]
RRAM updates:  91%|█████████▏| 91452/100000 [00:51<00:04, 1737.31it/s]
RRAM updates:  92%|█████████▏| 91626/100000 [00:51<00:04, 1719.55it/s]
RRAM updates:  92%|█████████▏| 91799/100000 [00:51<00:04, 1697.28it/s]
RRAM updates:  92%|█████████▏| 91969/100000 [00:51<00:04, 1691.82it/s]
RRAM updates:  92%|█████████▏| 92146/100000 [00:51<00:04, 1714.00it/s]
RRAM updates:  92%|█████████▏| 92325/100000 [00:51<00:04, 1733.97it/s]
RRAM updates:  93%|█████████▎| 92503/100000 [00:51<00:04, 1745.26it/s]
RRAM updates:  93%|█████████▎| 92681/100000 [00:51<00:04, 1754.77it/s]
RRAM updates:  93%|█████████▎| 92859/100000 [00:52<00:04, 1762.24it/s]
RRAM updates:  93%|█████████▎| 93037/100000 [00:52<00:03, 1765.97it/s]
RRAM updates:  93%|█████████▎| 93216/100000 [00:52<00:03, 1772.13it/s]
RRAM updates:  93%|█████████▎| 93395/100000 [00:52<00:03, 1776.92it/s]
RRAM updates:  94%|█████████▎| 93574/100000 [00:52<00:03, 1779.22it/s]
RRAM updates:  94%|█████████▍| 93752/100000 [00:52<00:03, 1779.07it/s]
RRAM updates:  94%|█████████▍| 93930/100000 [00:52<00:03, 1776.47it/s]
RRAM updates:  94%|█████████▍| 94109/100000 [00:52<00:03, 1777.72it/s]
RRAM updates:  94%|█████████▍| 94287/100000 [00:52<00:03, 1771.86it/s]
RRAM updates:  94%|█████████▍| 94465/100000 [00:52<00:03, 1773.58it/s]
RRAM updates:  95%|█████████▍| 94643/100000 [00:53<00:03, 1771.96it/s]
RRAM updates:  95%|█████████▍| 94822/100000 [00:53<00:02, 1775.23it/s]
RRAM updates:  95%|█████████▌| 95001/100000 [00:53<00:02, 1776.87it/s]
RRAM updates:  95%|█████████▌| 95181/100000 [00:53<00:02, 1781.60it/s]
RRAM updates:  95%|█████████▌| 95360/100000 [00:53<00:02, 1783.99it/s]
RRAM updates:  96%|█████████▌| 95539/100000 [00:53<00:02, 1782.14it/s]
RRAM updates:  96%|█████████▌| 95718/100000 [00:53<00:02, 1781.90it/s]
RRAM updates:  96%|█████████▌| 95897/100000 [00:53<00:02, 1782.17it/s]
RRAM updates:  96%|█████████▌| 96076/100000 [00:53<00:02, 1779.70it/s]
RRAM updates:  96%|█████████▋| 96255/100000 [00:53<00:02, 1780.03it/s]
RRAM updates:  96%|█████████▋| 96434/100000 [00:54<00:02, 1781.46it/s]
RRAM updates:  97%|█████████▋| 96613/100000 [00:54<00:01, 1781.32it/s]
RRAM updates:  97%|█████████▋| 96792/100000 [00:54<00:01, 1781.24it/s]
RRAM updates:  97%|█████████▋| 96971/100000 [00:54<00:01, 1778.02it/s]
RRAM updates:  97%|█████████▋| 97149/100000 [00:54<00:01, 1776.42it/s]
RRAM updates:  97%|█████████▋| 97328/100000 [00:54<00:01, 1778.39it/s]
RRAM updates:  98%|█████████▊| 97506/100000 [00:54<00:01, 1777.36it/s]
RRAM updates:  98%|█████████▊| 97685/100000 [00:54<00:01, 1779.13it/s]
RRAM updates:  98%|█████████▊| 97864/100000 [00:54<00:01, 1780.63it/s]
RRAM updates:  98%|█████████▊| 98043/100000 [00:54<00:01, 1780.46it/s]
RRAM updates:  98%|█████████▊| 98222/100000 [00:55<00:00, 1783.10it/s]
RRAM updates:  98%|█████████▊| 98401/100000 [00:55<00:00, 1782.08it/s]
RRAM updates:  99%|█████████▊| 98580/100000 [00:55<00:00, 1782.22it/s]
RRAM updates:  99%|█████████▉| 98759/100000 [00:55<00:00, 1781.45it/s]
RRAM updates:  99%|█████████▉| 98938/100000 [00:55<00:00, 1779.99it/s]
RRAM updates:  99%|█████████▉| 99116/100000 [00:55<00:00, 1778.98it/s]
RRAM updates:  99%|█████████▉| 99295/100000 [00:55<00:00, 1780.98it/s]
RRAM updates:  99%|█████████▉| 99475/100000 [00:55<00:00, 1784.93it/s]
RRAM updates: 100%|█████████▉| 99654/100000 [00:55<00:00, 1786.13it/s]
RRAM updates: 100%|█████████▉| 99833/100000 [00:55<00:00, 1785.59it/s]
RRAM updates: 100%|██████████| 100000/100000 [00:56<00:00, 1785.10it/s]
accuracy_test/rram_write_test.py:168: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at ../aten/src/ATen/native/ReduceOps.cpp:1808.)
  ideal_std = self.ideal_weights.std().item()
accuracy_test/rram_write_test.py:169: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at ../aten/src/ATen/native/ReduceOps.cpp:1808.)
  rram_std = self.rram_weights.std().item()

[Update 1000] RMSE: 0.329970, Max drift: 0.329970, Clamps: 0

[Update 2000] RMSE: 0.497151, Max drift: 0.497151, Clamps: 0

[Update 3000] RMSE: 0.585976, Max drift: 0.585976, Clamps: 0

[Update 4000] RMSE: 0.595665, Max drift: 0.595665, Clamps: 0

[Update 5000] RMSE: 0.687966, Max drift: 0.687966, Clamps: 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 6000] RMSE: 0.836297, Max drift: 0.836297, Clamps: 23
Clamp detected in cell 0
Clamp detected in cell 0

[Update 7000] RMSE: 0.871783, Max drift: 0.871783, Clamps: 25
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 8000] RMSE: 0.893884, Max drift: 0.893884, Clamps: 53
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 9000] RMSE: 0.780787, Max drift: 0.780787, Clamps: 93

[Update 10000] RMSE: 0.677818, Max drift: 0.677818, Clamps: 93

[Update 11000] RMSE: 0.787192, Max drift: 0.787192, Clamps: 93

[Update 12000] RMSE: 0.683792, Max drift: 0.683792, Clamps: 93

[Update 13000] RMSE: 0.630374, Max drift: 0.630374, Clamps: 93

[Update 14000] RMSE: 0.538726, Max drift: 0.538726, Clamps: 93

[Update 15000] RMSE: 0.632311, Max drift: 0.632311, Clamps: 93

[Update 16000] RMSE: 0.575711, Max drift: 0.575711, Clamps: 93

[Update 17000] RMSE: 0.332317, Max drift: 0.332317, Clamps: 93

[Update 18000] RMSE: 0.318463, Max drift: 0.318463, Clamps: 93

[Update 19000] RMSE: 0.095302, Max drift: 0.095302, Clamps: 93

[Update 20000] RMSE: 0.286257, Max drift: 0.286257, Clamps: 93

[Update 21000] RMSE: 0.107978, Max drift: 0.107978, Clamps: 93

[Update 22000] RMSE: 0.130668, Max drift: 0.130668, Clamps: 93

[Update 23000] RMSE: 0.056460, Max drift: 0.056460, Clamps: 93

[Update 24000] RMSE: 0.043195, Max drift: 0.043195, Clamps: 93

[Update 25000] RMSE: 0.067032, Max drift: 0.067032, Clamps: 93

[Update 26000] RMSE: 0.427203, Max drift: 0.427203, Clamps: 93

[Update 27000] RMSE: 0.659463, Max drift: 0.659463, Clamps: 93

[Update 28000] RMSE: 0.757952, Max drift: 0.757952, Clamps: 93

[Update 29000] RMSE: 0.517068, Max drift: 0.517068, Clamps: 93

[Update 30000] RMSE: 0.236891, Max drift: 0.236891, Clamps: 93

[Update 31000] RMSE: 0.008359, Max drift: 0.008359, Clamps: 93

[Update 32000] RMSE: 0.246794, Max drift: 0.246794, Clamps: 93

[Update 33000] RMSE: 0.142284, Max drift: 0.142284, Clamps: 93

[Update 34000] RMSE: 0.253204, Max drift: 0.253204, Clamps: 93

[Update 35000] RMSE: 0.478752, Max drift: 0.478752, Clamps: 93

[Update 36000] RMSE: 0.510194, Max drift: 0.510194, Clamps: 93

[Update 37000] RMSE: 0.426059, Max drift: 0.426059, Clamps: 93

[Update 38000] RMSE: 0.385290, Max drift: 0.385290, Clamps: 93

[Update 39000] RMSE: 0.195052, Max drift: 0.195052, Clamps: 93

[Update 40000] RMSE: 0.041224, Max drift: 0.041224, Clamps: 93

[Update 41000] RMSE: 0.197734, Max drift: 0.197734, Clamps: 93

[Update 42000] RMSE: 0.288096, Max drift: 0.288096, Clamps: 93

[Update 43000] RMSE: 0.252254, Max drift: 0.252254, Clamps: 93

[Update 44000] RMSE: 0.122498, Max drift: 0.122498, Clamps: 93

[Update 45000] RMSE: 0.449583, Max drift: 0.449583, Clamps: 93

[Update 46000] RMSE: 0.287523, Max drift: 0.287523, Clamps: 93

[Update 47000] RMSE: 0.311891, Max drift: 0.311891, Clamps: 93

[Update 48000] RMSE: 0.211224, Max drift: 0.211224, Clamps: 93

[Update 49000] RMSE: 0.382076, Max drift: 0.382076, Clamps: 93

[Update 50000] RMSE: 0.348744, Max drift: 0.348744, Clamps: 93

[Update 51000] RMSE: 0.303270, Max drift: 0.303270, Clamps: 93

[Update 52000] RMSE: 0.596848, Max drift: 0.596848, Clamps: 93

[Update 53000] RMSE: 0.756041, Max drift: 0.756041, Clamps: 93

[Update 54000] RMSE: 0.552008, Max drift: 0.552008, Clamps: 93

[Update 55000] RMSE: 0.430268, Max drift: 0.430268, Clamps: 93

[Update 56000] RMSE: 0.515931, Max drift: 0.515931, Clamps: 93

[Update 57000] RMSE: 0.468746, Max drift: 0.468746, Clamps: 93

[Update 58000] RMSE: 0.552018, Max drift: 0.552018, Clamps: 93

[Update 59000] RMSE: 0.429797, Max drift: 0.429797, Clamps: 93

[Update 60000] RMSE: 0.372257, Max drift: 0.372257, Clamps: 93

[Update 61000] RMSE: 0.226921, Max drift: 0.226921, Clamps: 93

[Update 62000] RMSE: 0.391867, Max drift: 0.391867, Clamps: 93

[Update 63000] RMSE: 0.477580, Max drift: 0.477580, Clamps: 93

[Update 64000] RMSE: 0.377717, Max drift: 0.377717, Clamps: 93

[Update 65000] RMSE: 0.202967, Max drift: 0.202967, Clamps: 93

[Update 66000] RMSE: 0.164595, Max drift: 0.164595, Clamps: 93

[Update 67000] RMSE: 0.409364, Max drift: 0.409364, Clamps: 93

[Update 68000] RMSE: 0.243076, Max drift: 0.243076, Clamps: 93

[Update 69000] RMSE: 0.486729, Max drift: 0.486729, Clamps: 93

[Update 70000] RMSE: 0.536506, Max drift: 0.536506, Clamps: 93

[Update 71000] RMSE: 0.554692, Max drift: 0.554692, Clamps: 93

[Update 72000] RMSE: 0.541011, Max drift: 0.541011, Clamps: 93
Clamp detected in cell 0

[Update 73000] RMSE: 0.876690, Max drift: 0.876690, Clamps: 94
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 74000] RMSE: 0.829713, Max drift: 0.829713, Clamps: 121
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 75000] RMSE: 0.875358, Max drift: 0.875358, Clamps: 147
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 76000] RMSE: 0.648387, Max drift: 0.648387, Clamps: 168

[Update 77000] RMSE: 0.849474, Max drift: 0.849474, Clamps: 168
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 78000] RMSE: 0.724581, Max drift: 0.724581, Clamps: 182
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 79000] RMSE: 0.839202, Max drift: 0.839202, Clamps: 194
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 80000] RMSE: 0.774070, Max drift: 0.774070, Clamps: 252

[Update 81000] RMSE: 0.549260, Max drift: 0.549260, Clamps: 252

[Update 82000] RMSE: 0.570597, Max drift: 0.570597, Clamps: 252

[Update 83000] RMSE: 0.627958, Max drift: 0.627958, Clamps: 252

[Update 84000] RMSE: 0.720585, Max drift: 0.720585, Clamps: 252
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 85000] RMSE: 0.791175, Max drift: 0.791175, Clamps: 258
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 86000] RMSE: 0.862634, Max drift: 0.862634, Clamps: 291

[Update 87000] RMSE: 0.897277, Max drift: 0.897277, Clamps: 291
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 88000] RMSE: 0.822737, Max drift: 0.822737, Clamps: 302
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 89000] RMSE: 0.905877, Max drift: 0.905877, Clamps: 391
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0
Clamp detected in cell 0

[Update 90000] RMSE: 0.719965, Max drift: 0.719965, Clamps: 395

[Update 91000] RMSE: 0.631198, Max drift: 0.631198, Clamps: 395

[Update 92000] RMSE: 0.736900, Max drift: 0.736900, Clamps: 395

[Update 93000] RMSE: 0.593394, Max drift: 0.593394, Clamps: 395

[Update 94000] RMSE: 0.518725, Max drift: 0.518725, Clamps: 395

[Update 95000] RMSE: 0.258058, Max drift: 0.258058, Clamps: 395

[Update 96000] RMSE: 0.019075, Max drift: 0.019075, Clamps: 395

[Update 97000] RMSE: 0.300353, Max drift: 0.300353, Clamps: 395

[Update 98000] RMSE: 0.620996, Max drift: 0.620996, Clamps: 395

[Update 99000] RMSE: 0.739774, Max drift: 0.739774, Clamps: 395

[Update 100000] RMSE: 0.594432, Max drift: 0.594432, Clamps: 395
Simulation completed, time elapsed: 56.0s

                ============================================================
                RRAM Write Test Results

                Weight Accuracy Degradation:
                    RMSE:                    0.594432
                    Max weight drift:        0.594432
                    Mean weight drift:       0.594432
                    Std deviation change:    nan

                Operation Statistics:
                    Total write operations:  100,000
                    Clamp events:            395 (39.50%%)
                    NaN events:              0 (0.00%%)
                    Failed writes:           0
                    Success rate:            100.00%

                Weight Distribution:
                    Ideal weight std:        nan
                    RRAM weight std:         nan
                    Ideal weights in range:  1/1
                    RRAM weights in range:   1/1

                Final RRAM Weights:
                    Mean:                    0.600309
                    Range:                   [0.600, 0.600]
                

============================================================
Process Data Summary
============================================================
Recording interval: every 1000 updates
Total data points: 100

Update    RMSE      Max Drift  Mean Drift  Clamps  Weight Range
-----------------------------------------------------------------
  1000  0.329970  0.329970   0.329970      0   [-0.324, -0.324]
  2000  0.497151  0.497151   0.497151      0   [-0.491, -0.491]
  3000  0.585976  0.585976   0.585976      0   [-0.580, -0.580]
  4000  0.595665  0.595665   0.595665      0   [-0.590, -0.590]
  5000  0.687966  0.687966   0.687966      0   [-0.682, -0.682]
  6000  0.836297  0.836297   0.836297     23   [-0.830, -0.830]
  7000  0.871783  0.871783   0.871783     25   [-0.866, -0.866]
  8000  0.893884  0.893884   0.893884     53   [-0.888, -0.888]
  9000  0.780787  0.780787   0.780787     93   [-0.775, -0.775]
 10000  0.677818  0.677818   0.677818     93   [-0.672, -0.672]
 11000  0.787192  0.787192   0.787192     93   [-0.781, -0.781]
 12000  0.683792  0.683792   0.683792     93   [-0.678, -0.678]
 13000  0.630374  0.630374   0.630374     93   [-0.624, -0.624]
 14000  0.538726  0.538726   0.538726     93   [-0.533, -0.533]
 15000  0.632311  0.632311   0.632311     93   [-0.626, -0.626]
 16000  0.575711  0.575711   0.575711     93   [-0.570, -0.570]
 17000  0.332317  0.332317   0.332317     93   [-0.326, -0.326]
 18000  0.318463  0.318463   0.318463     93   [-0.313, -0.313]
 19000  0.095302  0.095302   0.095302     93   [-0.089, -0.089]
 20000  0.286257  0.286257   0.286257     93   [-0.280, -0.280]
 21000  0.107978  0.107978   0.107978     93   [-0.102, -0.102]
 22000  0.130668  0.130668   0.130668     93   [0.137, 0.137]
 23000  0.056460  0.056460   0.056460     93   [0.062, 0.062]
 24000  0.043195  0.043195   0.043195     93   [0.049, 0.049]
 25000  0.067032  0.067032   0.067032     93   [0.073, 0.073]
 26000  0.427203  0.427203   0.427203     93   [0.433, 0.433]
 27000  0.659463  0.659463   0.659463     93   [0.665, 0.665]
 28000  0.757952  0.757952   0.757952     93   [0.764, 0.764]
 29000  0.517068  0.517068   0.517068     93   [0.523, 0.523]
 30000  0.236891  0.236891   0.236891     93   [0.243, 0.243]
 31000  0.008359  0.008359   0.008359     93   [-0.002, -0.002]
 32000  0.246794  0.246794   0.246794     93   [0.253, 0.253]
 33000  0.142284  0.142284   0.142284     93   [0.148, 0.148]
 34000  0.253204  0.253204   0.253204     93   [0.259, 0.259]
 35000  0.478752  0.478752   0.478752     93   [0.485, 0.485]
 36000  0.510194  0.510194   0.510194     93   [0.516, 0.516]
 37000  0.426059  0.426059   0.426059     93   [0.432, 0.432]
 38000  0.385290  0.385290   0.385290     93   [0.391, 0.391]
 39000  0.195052  0.195052   0.195052     93   [0.201, 0.201]
 40000  0.041224  0.041224   0.041224     93   [0.047, 0.047]
 41000  0.197734  0.197734   0.197734     93   [-0.192, -0.192]
 42000  0.288096  0.288096   0.288096     93   [0.294, 0.294]
 43000  0.252254  0.252254   0.252254     93   [0.258, 0.258]
 44000  0.122498  0.122498   0.122498     93   [0.128, 0.128]
 45000  0.449583  0.449583   0.449583     93   [0.455, 0.455]
 46000  0.287523  0.287523   0.287523     93   [0.293, 0.293]
 47000  0.311891  0.311891   0.311891     93   [0.318, 0.318]
 48000  0.211224  0.211224   0.211224     93   [-0.205, -0.205]
 49000  0.382076  0.382076   0.382076     93   [-0.376, -0.376]
 50000  0.348744  0.348744   0.348744     93   [-0.343, -0.343]
 51000  0.303270  0.303270   0.303270     93   [-0.297, -0.297]
 52000  0.596848  0.596848   0.596848     93   [-0.591, -0.591]
 53000  0.756041  0.756041   0.756041     93   [-0.750, -0.750]
 54000  0.552008  0.552008   0.552008     93   [-0.546, -0.546]
 55000  0.430268  0.430268   0.430268     93   [-0.424, -0.424]
 56000  0.515931  0.515931   0.515931     93   [-0.510, -0.510]
 57000  0.468746  0.468746   0.468746     93   [-0.463, -0.463]
 58000  0.552018  0.552018   0.552018     93   [-0.546, -0.546]
 59000  0.429797  0.429797   0.429797     93   [-0.424, -0.424]
 60000  0.372257  0.372257   0.372257     93   [-0.366, -0.366]
 61000  0.226921  0.226921   0.226921     93   [-0.221, -0.221]
 62000  0.391867  0.391867   0.391867     93   [-0.386, -0.386]
 63000  0.477580  0.477580   0.477580     93   [-0.472, -0.472]
 64000  0.377717  0.377717   0.377717     93   [-0.372, -0.372]
 65000  0.202967  0.202967   0.202967     93   [-0.197, -0.197]
 66000  0.164595  0.164595   0.164595     93   [-0.159, -0.159]
 67000  0.409364  0.409364   0.409364     93   [-0.403, -0.403]
 68000  0.243076  0.243076   0.243076     93   [-0.237, -0.237]
 69000  0.486729  0.486729   0.486729     93   [-0.481, -0.481]
 70000  0.536506  0.536506   0.536506     93   [-0.531, -0.531]
 71000  0.554692  0.554692   0.554692     93   [-0.549, -0.549]
 72000  0.541011  0.541011   0.541011     93   [-0.535, -0.535]
 73000  0.876690  0.876690   0.876690     94   [-0.871, -0.871]
 74000  0.829713  0.829713   0.829713    121   [-0.824, -0.824]
 75000  0.875358  0.875358   0.875358    147   [-0.869, -0.869]
 76000  0.648387  0.648387   0.648387    168   [-0.643, -0.643]
 77000  0.849474  0.849474   0.849474    168   [-0.844, -0.844]
 78000  0.724581  0.724581   0.724581    182   [-0.719, -0.719]
 79000  0.839202  0.839202   0.839202    194   [-0.833, -0.833]
 80000  0.774070  0.774070   0.774070    252   [-0.768, -0.768]
 81000  0.549260  0.549260   0.549260    252   [-0.543, -0.543]
 82000  0.570597  0.570597   0.570597    252   [-0.565, -0.565]
 83000  0.627958  0.627958   0.627958    252   [-0.622, -0.622]
 84000  0.720585  0.720585   0.720585    252   [-0.715, -0.715]
 85000  0.791175  0.791175   0.791175    258   [-0.785, -0.785]
 86000  0.862634  0.862634   0.862634    291   [-0.857, -0.857]
 87000  0.897277  0.897277   0.897277    291   [-0.891, -0.891]
 88000  0.822737  0.822737   0.822737    302   [-0.817, -0.817]
 89000  0.905877  0.905877   0.905877    391   [-0.900, -0.900]
 90000  0.719965  0.719965   0.719965    395   [-0.714, -0.714]
 91000  0.631198  0.631198   0.631198    395   [-0.625, -0.625]
 92000  0.736900  0.736900   0.736900    395   [-0.731, -0.731]
 93000  0.593394  0.593394   0.593394    395   [-0.588, -0.588]
 94000  0.518725  0.518725   0.518725    395   [-0.513, -0.513]
 95000  0.258058  0.258058   0.258058    395   [-0.252, -0.252]
 96000  0.019075  0.019075   0.019075    395   [0.025, 0.025]
 97000  0.300353  0.300353   0.300353    395   [0.306, 0.306]
 98000  0.620996  0.620996   0.620996    395   [0.627, 0.627]
 99000  0.739774  0.739774   0.739774    395   [0.746, 0.746]
100000  0.594432  0.594432   0.594432    395   [0.600, 0.600]

Trends:
  RMSE change: +0.264462
  Max drift change: +0.264462
  Total clamp events: 395
  Total NaN events: 0
============================================================
