import os
import sys
import time
import numpy as np
import torch
from tqdm import tqdm

script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'PIM-Frontend'))

try:
    from pim import wage_quantizer
    print("Successfully imported all required modules.")
except ImportError as e:
    print(f"!!Error: Could not import required modules: {e}")
    raise

class RRAMTest:
    def __init__(
        self, 
        total_cells=1024, 
        d2d_variation=0.01, 
        c2c_variation=0.003, 
        adc_precision=5
    ):

        # RRAM physical parameters
        self.total_cells = total_cells
        self.d2dVari = d2d_variation
        self.c2cVari = c2c_variation
        self.ADCprecision = adc_precision
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Ideal weights (reference)
        self.ideal_weights = torch.zeros(total_cells, device=self.device, dtype=torch.float64)  
        # RRAM weights (actual)
        self.rram_weights = torch.zeros(total_cells, device=self.device, dtype=torch.float32) 

        self.write_operations = 0
        self.clamp_events = 0
        self.nan_events = 0
        self.failed_writes = 0

        # Process data recording
        self.process_data = []  # Store periodic snapshots
        self.recording_interval = 500  # Record every 500 updates

        print(f"--Total cells: {total_cells}, D2D variation: {d2d_variation}, C2C variation: {c2c_variation}, ADC: {adc_precision} bits")
    
    def initialize_weights(self, weight_scale=0.1):
        print(f"Initializing weights with scale: {weight_scale}")

        self.ideal_weights = torch.normal(mean=0.0, std=weight_scale, size=(self.total_cells,), device=self.device, dtype=torch.float64)
        self.ideal_weights = torch.clamp(self.ideal_weights, -0.8, 0.8) # for clamp

        self.rram_weights = self.ideal_weights.float().clone()

        print(f"Ideal weights: mean={self.ideal_weights.mean().item():.6f}, "
              f"std={self.ideal_weights.std().item():.6f}, "
              f"range=[{self.ideal_weights.min().item():.3f}, {self.ideal_weights.max().item():.3f}]")

    def rram_weight_update(self, cell_ids, weight_updates):
        if not isinstance(cell_ids, list):
            cell_ids = [cell_ids]
        
        for i, cell_id in enumerate(cell_ids):
            if cell_id >= self.total_cells:
                raise ValueError(f"Cell ID {cell_id} is out of bounds!!")
            
            current_weight = self.rram_weights[cell_id:cell_id+1]
            update_amount = weight_updates.flatten()[i] if weight_updates.numel() > i else weight_updates.flatten()[0]
            update_amount = torch.clamp(update_amount, -0.1, 0.1).unsqueeze(0) # TODO: 0.1 is ok?
            
            try:
                weight_change = wage_quantizer.NonlinearWeightUpdateWithD2DVariations(
                    origin=current_weight,
                    param=update_amount,
                    d2dVari=self.d2dVari,
                    nonlinearityLTP=1.75,
                    nonlinearityLTD=1.46,
                    max_level=16
                )

                updated_weight = current_weight - weight_change

                if self.c2cVari > 0:
                    updated_weight = wage_quantizer.NonlinearWeightUpdateWithC2CVariations(
                        updated_weight, self.c2cVari
                    )

                # Check NaN
                if torch.isnan(updated_weight).any():
                    self.nan_events += 1
                    print (f"NaN detected in cell {cell_id}")
                    updated_weight = current_weight

                # Check Clamp
                final_weight = torch.clamp(updated_weight, -0.9, 0.9)
                if not torch.equal(final_weight, updated_weight):
                    print (f"Clamp detected in cell {cell_id}")
                    self.clamp_events += 1

                self.rram_weights[cell_id] = final_weight.item()
                self.write_operations += 1

            except Exception as e:
                self.failed_writes += 1
                print (f"Error in PIM-Frontend call for cell {cell_id}: {e}")

    def simulate_write_operations(self, num_cycles=50, updates_per_cycle=100): # TIME
        total_updates = num_cycles * updates_per_cycle
        print(f"Starting simulation: {num_cycles} cycles * {updates_per_cycle} updates per cycle = {total_updates} total updates")

        start_time = time.time()

        update_count = 0
        with tqdm(total=total_updates, desc="RRAM updates") as pbar:
            for cycle in range(num_cycles):
                for _ in range(updates_per_cycle):
                    num_weights = min(500, self.total_cells)            # randomly select cells to update
                    cell_ids = np.random.choice(self.total_cells, num_weights, replace=False).tolist()
                    weight_updates = torch.normal(mean=0.0, std=0.01, size=(num_weights,), device=self.device)
                    self.rram_weight_update(cell_ids, weight_updates)   # update
                    update_count += 1
                    pbar.update(1)

                    if update_count % self.recording_interval == 0:
                        self.record_process_data(update_count)

        duration = time.time() - start_time
        print(f"Simulation completed, time elapsed: {duration:.1f}s")

    def record_process_data(self, update_count):
        # Compute current metrics vs ideal weights
        weight_diff = self.ideal_weights.float() - self.rram_weights
        current_rmse = torch.sqrt(torch.mean(weight_diff ** 2)).item()
        current_max_drift = torch.max(torch.abs(weight_diff)).item()
        current_mean_drift = torch.mean(torch.abs(weight_diff)).item()

        # Record data point
        data_point = {
            'update_count': update_count,
            'rmse': current_rmse,
            'max_drift': current_max_drift,
            'mean_drift': current_mean_drift,
            'clamp_events': self.clamp_events,
            'nan_events': self.nan_events,
            'rram_weight_mean': self.rram_weights.mean().item(),
            'rram_weight_std': self.rram_weights.std().item(),
            'rram_weight_min': self.rram_weights.min().item(),
            'rram_weight_max': self.rram_weights.max().item()
        }

        self.process_data.append(data_point)

        print(f"\n[Update {update_count}] RMSE: {current_rmse:.6f}, Max drift: {current_max_drift:.6f}, Clamps: {self.clamp_events}")

    def compute_key_metrics(self):
        # Weight accuracy changes
        weight_diff = self.ideal_weights.float() - self.rram_weights
        rmse = torch.sqrt(torch.mean(weight_diff ** 2)).item()
        max_drift = torch.max(torch.abs(weight_diff)).item()
        mean_drift = torch.mean(torch.abs(weight_diff)).item()

        # Weight distribution changes
        ideal_std = self.ideal_weights.std().item()
        rram_std = self.rram_weights.std().item()
        std_change = abs(rram_std - ideal_std)

        # Operation statistics
        total_ops = self.write_operations
        clamp_rate = (self.clamp_events / total_ops * 10000) if total_ops > 0 else 0.0
        nan_rate = (self.nan_events / total_ops * 10000) if total_ops > 0 else 0.0
        success_rate = ((total_ops - self.failed_writes) / total_ops * 100) if total_ops > 0 else 0.0

        # Weight range statistics
        ideal_in_range = torch.sum((self.ideal_weights >= -0.9) & (self.ideal_weights <= 0.9)).item()
        rram_in_range = torch.sum((self.rram_weights >= -0.9) & (self.rram_weights <= 0.9)).item()
        
        return {
            'rmse': rmse,
            'max_weight_drift': max_drift,
            'mean_weight_drift': mean_drift,
            'std_change': std_change,

            'total_write_ops': total_ops,
            'clamp_events': self.clamp_events,
            'nan_events': self.nan_events,
            'failed_writes': self.failed_writes,

            'clamp_rate': clamp_rate,
            'nan_rate': nan_rate,
            'success_rate': success_rate,

            'ideal_std': ideal_std,
            'rram_std': rram_std,
            'ideal_in_range': ideal_in_range,
            'rram_in_range': rram_in_range,

            'final_rram_mean': self.rram_weights.mean().item(),
            'final_rram_min': self.rram_weights.min().item(),
            'final_rram_max': self.rram_weights.max().item()
        }

    def print_results(self, metrics):
        print(f"""
                {'='*60}
                RRAM Write Test Results

                Weight Accuracy Degradation:
                    RMSE:                    {metrics['rmse']:.6f}
                    Max weight drift:        {metrics['max_weight_drift']:.6f}
                    Mean weight drift:       {metrics['mean_weight_drift']:.6f}
                    Std deviation change:    {metrics['std_change']:.6f}

                Operation Statistics:
                    Total write operations:  {metrics['total_write_ops']:,}
                    Clamp events:            {metrics['clamp_events']:,} ({metrics['clamp_rate']:.2f}%%)
                    NaN events:              {metrics['nan_events']:,} ({metrics['nan_rate']:.2f}%%)
                    Failed writes:           {metrics['failed_writes']:,}
                    Success rate:            {metrics['success_rate']:.2f}%

                Weight Distribution:
                    Ideal weight std:        {metrics['ideal_std']:.6f}
                    RRAM weight std:         {metrics['rram_std']:.6f}
                    Ideal weights in range:  {metrics['ideal_in_range']:,}/{self.total_cells}
                    RRAM weights in range:   {metrics['rram_in_range']:,}/{self.total_cells}

                Final RRAM Weights:
                    Mean:                    {metrics['final_rram_mean']:.6f}
                    Range:                   [{metrics['final_rram_min']:.3f}, {metrics['final_rram_max']:.3f}]
                """
            )

    def print_process_data_summary(self):
        """Print summary of recorded process data"""
        if not self.process_data:
            print("No process data recorded.")
            return

        print(f"\n{'='*60}")
        print("Process Data Summary")
        print(f"{'='*60}")
        print(f"Recording interval: every {self.recording_interval} updates")
        print(f"Total data points: {len(self.process_data)}")
        print()

        print("Update    RMSE      Max Drift  Mean Drift  Clamps  Weight Range")
        print("-" * 65)
        for data in self.process_data:
            print(f"{data['update_count']:6d}  {data['rmse']:.6f}  {data['max_drift']:.6f}   {data['mean_drift']:.6f}   {data['clamp_events']:4d}   [{data['rram_weight_min']:.3f}, {data['rram_weight_max']:.3f}]")

        # Show trends
        if len(self.process_data) >= 2:
            first = self.process_data[0]
            last = self.process_data[-1]
            rmse_change = last['rmse'] - first['rmse']
            drift_change = last['max_drift'] - first['max_drift']

            print(f"\nTrends:")
            print(f"  RMSE change: {rmse_change:+.6f}")
            print(f"  Max drift change: {drift_change:+.6f}")
            print(f"  Total clamp events: {last['clamp_events']}")
            print(f"  Total NaN events: {last['nan_events']}")

        print(f"{'='*60}")

def main():
    import argparse

    parser = argparse.ArgumentParser(description='RRAM Weight Storage Lifetime Test')
    parser.add_argument('--cycles', type=int, default=100000000, help='Number of Test cycles')
    parser.add_argument('--updates-per-cycle', type=int, default=1, help='Updates per cycle')
    parser.add_argument('--cells', type=int, default=1, help='Number of RRAM cells')
    parser.add_argument('--d2d-var', type=float, default=0.01, help='D2D variation')
    parser.add_argument('--c2c-var', type=float, default=0.003, help='C2C variation')
    parser.add_argument('--adc-bits', type=int, default=5, help='ADC precision bits')
    parser.add_argument('--weight-scale', type=float, default=0.1, help='Weight scale')
    parser.add_argument('--record-interval', type=int, default=1, help='Process data recording interval')

    args = parser.parse_args()
    print(f"Configuration: {args.cycles} cycles, {args.updates_per_cycle} updates/cycle, {args.cells} cells")

    tester = RRAMTest(
        total_cells=args.cells,
        d2d_variation=args.d2d_var,
        c2c_variation=args.c2c_var,
        adc_precision=args.adc_bits
    )

    # Set recording interval
    tester.recording_interval = args.record_interval

    tester.initialize_weights(args.weight_scale)
    tester.simulate_write_operations(args.cycles, args.updates_per_cycle)
    metrics = tester.compute_key_metrics()
    tester.print_results(metrics)

    # Print process data summary
    tester.print_process_data_summary()

    return metrics

if __name__ == "__main__":
    main()
