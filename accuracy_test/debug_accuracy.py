import os
import sys
import torch

# Add project paths
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'PIM-Frontend'))

try:
    from comprehensive_accuracy_evaluator import ComprehensiveAccuracyEvaluator
    print("Successfully imported accuracy evaluator.")
except ImportError as e:
    print(f"Error importing: {e}")
    raise

def debug_accuracy_computation():
    """
    Debug the accuracy computation to find why RMSE is infinite
    """
    print("="*60)
    print("Debugging Accuracy Computation")
    print("="*60)
    
    # Create evaluator
    evaluator = ComprehensiveAccuracyEvaluator()
    
    # Manually add some test data
    print("\n1. Adding test reference and RRAM results...")
    
    # Add reference results
    for i in range(3):
        key = f"test_op_{i}"
        ref_result = torch.randn(4, 1, dtype=torch.float64) * 0.1
        evaluator.reference_results[key] = {
            'result': ref_result,
            'input': torch.randn(4, 1),
            'operation_type': 'StaticVMM',
            'matrix_size': (4, 1),
            'timestamp': 0.001
        }
        
        # Add corresponding RRAM result with some noise
        rram_result = ref_result.float() + torch.randn_like(ref_result.float()) * 0.01
        evaluator.rram_results[key] = {
            'result': rram_result,
            'metadata': {'operation_type': 'StaticVMM'},
            'timestamp': 0.001
        }
        
        print(f"  Added pair {i}: ref_shape={ref_result.shape}, rram_shape={rram_result.shape}")
        print(f"    Ref norm: {ref_result.norm().item():.6f}")
        print(f"    RRAM norm: {rram_result.norm().item():.6f}")
        print(f"    Difference: {(ref_result.double() - rram_result.double()).norm().item():.6f}")
    
    # Check what we have
    print(f"\n2. Data check:")
    print(f"  Reference results: {len(evaluator.reference_results)}")
    print(f"  RRAM results: {len(evaluator.rram_results)}")
    print(f"  Reference keys: {list(evaluator.reference_results.keys())}")
    print(f"  RRAM keys: {list(evaluator.rram_results.keys())}")
    
    # Test precision computation
    print(f"\n3. Testing precision computation...")
    try:
        precision_metrics = evaluator.compute_numerical_precision_metrics()
        print(f"  RMSE: {precision_metrics['rmse']}")
        print(f"  Relative Error: {precision_metrics['relative_error_percent']}%")
        print(f"  Max Absolute Error: {precision_metrics['max_absolute_error']}")
        print(f"  Mean Absolute Error: {precision_metrics['mean_absolute_error']}")
        
        if precision_metrics['rmse'] == float('inf'):
            print("  ❌ RMSE is still infinite!")
            
            # Debug step by step
            print(f"\n4. Step-by-step debugging...")
            for key in evaluator.reference_results:
                if key in evaluator.rram_results:
                    ref_result = evaluator.reference_results[key]['result']
                    rram_result = evaluator.rram_results[key]['result']
                    
                    print(f"  Key: {key}")
                    print(f"    Ref shape: {ref_result.shape}, dtype: {ref_result.dtype}")
                    print(f"    RRAM shape: {rram_result.shape}, dtype: {rram_result.dtype}")
                    
                    if ref_result.shape == rram_result.shape:
                        ref_result = ref_result.float()
                        rram_result = rram_result.float()
                        
                        absolute_error = torch.abs(ref_result - rram_result)
                        squared_error = torch.pow(ref_result - rram_result, 2)
                        
                        print(f"    Absolute error: {absolute_error.mean().item():.6f}")
                        print(f"    Squared error: {squared_error.mean().item():.6f}")
                        print(f"    RMSE for this pair: {torch.sqrt(torch.mean(squared_error)).item():.6f}")
                    else:
                        print(f"    ❌ Shape mismatch!")
                else:
                    print(f"  ❌ No RRAM result for key: {key}")
        else:
            print(f"  ✅ RMSE computation successful!")
            
    except Exception as e:
        print(f"  ❌ Error in precision computation: {e}")
        import traceback
        traceback.print_exc()

def test_simple_case():
    """
    Test with the simplest possible case
    """
    print(f"\n" + "="*60)
    print("Testing Simplest Case")
    print("="*60)
    
    evaluator = ComprehensiveAccuracyEvaluator()
    
    # Create identical results (should give RMSE = 0)
    ref_result = torch.tensor([[1.0], [2.0], [3.0]], dtype=torch.float64)
    rram_result = torch.tensor([[1.0], [2.0], [3.0]], dtype=torch.float32)
    
    key = "simple_test"
    evaluator.reference_results[key] = {
        'result': ref_result,
        'input': torch.zeros(3, 1),
        'operation_type': 'test',
        'matrix_size': (3, 1),
        'timestamp': 0.0
    }
    
    evaluator.rram_results[key] = {
        'result': rram_result,
        'metadata': {'operation_type': 'test'},
        'timestamp': 0.0
    }
    
    print(f"Reference: {ref_result.flatten()}")
    print(f"RRAM: {rram_result.flatten()}")
    
    metrics = evaluator.compute_numerical_precision_metrics()
    print(f"RMSE: {metrics['rmse']}")
    print(f"Should be 0 or very close to 0")
    
    if metrics['rmse'] < 1e-6:
        print("✅ Simple case works!")
    else:
        print("❌ Even simple case fails!")

def main():
    debug_accuracy_computation()
    test_simple_case()

if __name__ == "__main__":
    main()
