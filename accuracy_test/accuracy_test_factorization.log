================================================================================
Square Root Iteration Convergence Test
================================================================================
x = 1.0, Exact sqrt = 1.00000000
Iterations | Approx Value | Absolute Error | Relative Error(%)
-----------------------------------------------------------------
         1 |   1.25000000 |       2.50e-01 |       25.0000
         2 |   1.02500000 |       2.50e-02 |        2.5000
         3 |   1.00030488 |       3.05e-04 |        0.0305
         5 |   1.00000000 |       1.11e-15 |        0.0000
        10 |   1.00000000 |       0.00e+00 |        0.0000

x = 4.0, Exact sqrt = 2.00000000
Iterations | Approx Value | Absolute Error | Relative Error(%)
-----------------------------------------------------------------
         1 |   2.00000000 |       0.00e+00 |        0.0000
         2 |   2.00000000 |       0.00e+00 |        0.0000
         3 |   2.00000000 |       0.00e+00 |        0.0000
         5 |   2.00000000 |       0.00e+00 |        0.0000
        10 |   2.00000000 |       0.00e+00 |        0.0000

x = 9.0, Exact sqrt = 3.00000000
Iterations | Approx Value | Absolute Error | Relative Error(%)
-----------------------------------------------------------------
         1 |   3.25000000 |       2.50e-01 |        8.3333
         2 |   3.00961538 |       9.62e-03 |        0.3205
         3 |   3.00001536 |       1.54e-05 |        0.0005
         5 |   3.00000000 |       0.00e+00 |        0.0000
        10 |   3.00000000 |       0.00e+00 |        0.0000

x = 16.0, Exact sqrt = 4.00000000
Iterations | Approx Value | Absolute Error | Relative Error(%)
-----------------------------------------------------------------
         1 |   5.00000000 |       1.00e+00 |       25.0000
         2 |   4.10000000 |       1.00e-01 |        2.5000
         3 |   4.00121951 |       1.22e-03 |        0.0305
         5 |   4.00000000 |       4.44e-15 |        0.0000
        10 |   4.00000000 |       0.00e+00 |        0.0000

x = 25.0, Exact sqrt = 5.00000000
Iterations | Approx Value | Absolute Error | Relative Error(%)
-----------------------------------------------------------------
         1 |   7.25000000 |       2.25e+00 |       45.0000
         2 |   5.34913793 |       3.49e-01 |        6.9828
         3 |   5.01139411 |       1.14e-02 |        0.2279
         5 |   5.00000000 |       1.68e-11 |        0.0000
        10 |   5.00000000 |       0.00e+00 |        0.0000

x = 0.1, Exact sqrt = 0.31622777
Iterations | Approx Value | Absolute Error | Relative Error(%)
-----------------------------------------------------------------
         1 |   1.02500000 |       7.09e-01 |      224.1335
         2 |   0.56128049 |       2.45e-01 |       77.4925
         3 |   0.36972226 |       5.35e-02 |       16.9164
         5 |   0.31625116 |       2.34e-05 |        0.0074
        10 |   0.31622777 |       0.00e+00 |        0.0000

x = 0.01, Exact sqrt = 0.10000000
Iterations | Approx Value | Absolute Error | Relative Error(%)
-----------------------------------------------------------------
         1 |   1.00250000 |       9.02e-01 |      902.5000
         2 |   0.50623753 |       4.06e-01 |      406.2375
         3 |   0.26299555 |       1.63e-01 |      162.9956
         5 |   0.10847525 |       8.48e-03 |        8.4752
        10 |   0.10000000 |       0.00e+00 |        0.0000

x = 100.0, Exact sqrt = 10.00000000
Iterations | Approx Value | Absolute Error | Relative Error(%)
-----------------------------------------------------------------
         1 |  26.00000000 |       1.60e+01 |      160.0000
         2 |  14.92307692 |       4.92e+00 |       49.2308
         3 |  10.81205393 |       8.12e-01 |        8.1205
         5 |  10.00004636 |       4.64e-05 |        0.0005
        10 |  10.00000000 |       0.00e+00 |        0.0000

x = 1000.0, Exact sqrt = 31.62277660
Iterations | Approx Value | Absolute Error | Relative Error(%)
-----------------------------------------------------------------
         1 | 251.00000000 |       2.19e+02 |      693.7317
         2 | 127.49203187 |       9.59e+01 |      303.1652
         3 |  67.66782966 |       3.60e+01 |      113.9845
         5 |  32.74064099 |       1.12e+00 |        3.5350
        10 |  31.62277660 |       0.00e+00 |        0.0000


================================================================================
SLAM Jacobian Matrix QR Decomposition Accuracy Test
================================================================================
================================================================================
QR Decomposition Accuracy Test
================================================================================
Original Matrix A:
[[ 1.         -0.01382643  0.06476885  0.15230299 -0.02341534 -0.0234137
   0.15792128  0.07674347 -0.04694744  0.054256   -0.04634177 -0.04657298]
 [ 0.02419623 -0.19132802 -0.17249178 -0.05622875 -0.10128311  0.03142473
  -0.09080241 -0.14123037  0.14656488 -0.02257763  0.00675282 -0.14247482]
 [-0.05443827  0.01109226 -0.11509936  0.0375698  -0.06006387 -0.02916937
  -0.06017066  0.18522782 -0.00134972 -0.10577109  0.08225449 -0.12208436]
 [ 0.02088636 -0.19596701 -0.1328186   0.01968612  0.07384666  0.01713683
  -0.01156483 -0.03011037 -0.1478522  -0.07198442 -0.04606388  0.10571222]
 [ 0.03436183 -0.17630402  0.0324084  -0.03850823 -0.0676922   0.06116763
   0.10309995  0.09312801 -0.08392175 -0.03092124  0.03312634  0.09755451]
 [-0.04791742 -0.0185659  -0.1106335  -0.11962066  0.08125258  0.135624
  -0.00720101  0.10035329  0.0361636  -0.06451198  0.03613956  0.15380366]]

NumPy Reference QR Decomposition:
Q_ref:
[[-9.96289733e-01  4.38853575e-02 -5.99976934e-02  1.21801502e-02
   2.14048926e-03  4.15714153e-02]
 [-2.41064527e-02 -5.85693564e-01 -3.55223803e-01  1.50098520e-01
  -5.17814104e-01 -4.89425139e-01]
 [ 5.42362919e-02  2.93753923e-02 -5.51443476e-01 -4.25034035e-01
  -3.63040878e-01  6.16159876e-01]
 [-2.08088655e-02 -6.00230951e-01 -1.54275063e-01 -4.14121170e-01
   6.66327384e-01 -6.89553151e-04]
 [-3.42343374e-02 -5.38660043e-01  5.75388050e-01  1.88868884e-01
  -2.35141167e-01  5.35386904e-01]
 [ 4.77396373e-02 -6.11749557e-02 -4.59623994e-01  7.67789261e-01
   3.17461402e-01  3.04043164e-01]]
R_ref:
[[-1.00372408  0.02821615 -0.07024022 -0.15314677  0.0271721   0.02501126
  -0.16204254 -0.06077888  0.05084297 -0.05977036  0.05201799  0.0450164 ]
 [ 0.          0.32550782  0.16952137  0.05696473  0.04369629 -0.07182105
   0.01019155  0.0532965   0.0437964   0.07630738  0.00402174 -0.04759296]
 [ 0.          0.          0.21084581  0.01990462 -0.01718275 -0.02345749
   0.12037739 -0.04447347 -0.09060162  0.0860566  -0.03542067  0.08985843]
 [ 0.          0.          0.         -0.12982213  0.02905999  0.12541619
   0.03260153  0.00811665  0.09514557  0.0166669   0.01856831  0.12267362]
 [ 0.          0.          0.          0.          0.16511918  0.03435849
   0.03496616 -0.00405322 -0.14280781 -0.01096802 -0.06046772  0.21432399]
 [ 0.          0.          0.          0.          0.          0.03964569
   0.06694817  0.26683399 -0.10834921 -0.08798596  0.07420558  0.09149063]]

Testing 3 iterations:
----------------------------------------
Q Error (Frobenius norm): 2.88e+00
R Error (Frobenius norm): 2.17e+00
Reconstruction Error ||QR - A||: 6.84e-01

Testing 5 iterations:
----------------------------------------
Q Error (Frobenius norm): 3.26e+00
R Error (Frobenius norm): 2.21e+00
Reconstruction Error ||QR - A||: 2.26e-01

Testing 10 iterations:
----------------------------------------
Q Error (Frobenius norm): 3.46e+00
R Error (Frobenius norm): 2.23e+00
Reconstruction Error ||QR - A||: 5.26e-16


================================================================================
Extreme Cases Test
================================================================================
================================================================================
QR Decomposition Accuracy Test
================================================================================
Original Matrix A:
[[1.e+00 0.e+00 0.e+00]
 [0.e+00 1.e-06 0.e+00]
 [0.e+00 0.e+00 1.e+06]]

NumPy Reference QR Decomposition:
Q_ref:
[[1. 0. 0.]
 [0. 1. 0.]
 [0. 0. 1.]]
R_ref:
[[1.e+00 0.e+00 0.e+00]
 [0.e+00 1.e-06 0.e+00]
 [0.e+00 0.e+00 1.e+06]]

Testing 3 iterations:
----------------------------------------
Q Error (Frobenius norm): 0.00e+00
R Error (Frobenius norm): 0.00e+00
Reconstruction Error ||QR - A||: 0.00e+00

Testing 5 iterations:
----------------------------------------
Q Error (Frobenius norm): 0.00e+00
R Error (Frobenius norm): 0.00e+00
Reconstruction Error ||QR - A||: 0.00e+00

Testing 10 iterations:
----------------------------------------
Q Error (Frobenius norm): 0.00e+00
R Error (Frobenius norm): 0.00e+00
Reconstruction Error ||QR - A||: 0.00e+00

