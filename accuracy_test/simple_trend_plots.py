#!/usr/bin/env python3
"""
简化的RRAM写入测试趋势分析工具
为每类数据创建独立的趋势图，使用数据采样提高性能
"""

import re
import matplotlib.pyplot as plt
import numpy as np
from typing import List, <PERSON><PERSON>

def parse_log_file_sampled(filename: str, sample_rate: int = 100) -> Tuple[List[int], List[float], List[float], List[int]]:
    """
    解析日志文件，提取数据（采样版本）
    
    Args:
        filename: 日志文件名
        sample_rate: 采样率，每sample_rate行取一行
    
    Returns:
        updates: 更新次数列表
        rmse_values: RMSE值列表
        max_drift_values: 最大漂移值列表
        clamps_values: 钳位次数列表
    """
    updates = []
    rmse_values = []
    max_drift_values = []
    clamps_values = []
    
    # 正则表达式匹配数据行
    pattern = r'\[Update (\d+)\] RMSE: ([\d.]+), Max drift: ([\d.]+), Clamps: (\d+)'
    
    line_count = 0
    with open(filename, 'r') as f:
        for line in f:
            match = re.search(pattern, line)
            if match:
                line_count += 1
                # 采样：每sample_rate行取一行
                if line_count % sample_rate == 0:
                    update_num = int(match.group(1))
                    rmse = float(match.group(2))
                    max_drift = float(match.group(3))
                    clamps = int(match.group(4))
                    
                    updates.append(update_num)
                    rmse_values.append(rmse)
                    max_drift_values.append(max_drift)
                    clamps_values.append(clamps)
    
    return updates, rmse_values, max_drift_values, clamps_values

def create_individual_trend_plots(updates: List[int], rmse_values: List[float], 
                                 max_drift_values: List[float], clamps_values: List[int]):
    """
    创建三个独立的趋势图文件
    """
    # 设置字体
    plt.rcParams['font.family'] = 'DejaVu Sans'
    
    # 1. RMSE趋势图
    plt.figure(figsize=(12, 6))
    plt.plot(updates, rmse_values, color='#2E86AB', linewidth=1.2, alpha=0.8)
    plt.title('RMSE Trend Over Time', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Update Number', fontsize=12)
    plt.ylabel('RMSE Value', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    rmse_mean = np.mean(rmse_values)
    rmse_std = np.std(rmse_values)
    rmse_max = np.max(rmse_values)
    rmse_min = np.min(rmse_values)
    
    plt.axhline(y=rmse_mean, color='red', linestyle='--', alpha=0.7, label=f'Mean: {rmse_mean:.4f}')
    plt.legend(loc='upper right')
    
    # 添加文本框显示统计信息
    stats_text = f'Statistics:\nMean: {rmse_mean:.4f}\nStd: {rmse_std:.4f}\nMax: {rmse_max:.4f}\nMin: {rmse_min:.4f}'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('rmse_trend.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 最大漂移趋势图
    plt.figure(figsize=(12, 6))
    plt.plot(updates, max_drift_values, color='#A23B72', linewidth=1.2, alpha=0.8)
    plt.title('Max Drift Trend Over Time', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Update Number', fontsize=12)
    plt.ylabel('Max Drift Value', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    drift_mean = np.mean(max_drift_values)
    drift_std = np.std(max_drift_values)
    drift_max = np.max(max_drift_values)
    drift_min = np.min(max_drift_values)
    
    plt.axhline(y=drift_mean, color='red', linestyle='--', alpha=0.7, label=f'Mean: {drift_mean:.4f}')
    plt.legend(loc='upper right')
    
    stats_text = f'Statistics:\nMean: {drift_mean:.4f}\nStd: {drift_std:.4f}\nMax: {drift_max:.4f}\nMin: {drift_min:.4f}'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('max_drift_trend.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 钳位次数趋势图
    plt.figure(figsize=(12, 6))
    plt.plot(updates, clamps_values, color='#F18F01', linewidth=1.2, alpha=0.8)
    plt.title('Clamps Count Trend Over Time', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Update Number', fontsize=12)
    plt.ylabel('Clamps Count', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    clamps_mean = np.mean(clamps_values)
    clamps_std = np.std(clamps_values)
    clamps_max = np.max(clamps_values)
    clamps_min = np.min(clamps_values)
    
    if clamps_mean > 0:
        plt.axhline(y=clamps_mean, color='red', linestyle='--', alpha=0.7, label=f'Mean: {clamps_mean:.2f}')
        plt.legend(loc='upper right')
    
    stats_text = f'Statistics:\nMean: {clamps_mean:.2f}\nStd: {clamps_std:.2f}\nMax: {clamps_max}\nMin: {clamps_min}'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('clamps_trend.png', dpi=300, bbox_inches='tight')
    plt.close()

def print_summary(updates: List[int], rmse_values: List[float], 
                 max_drift_values: List[float], clamps_values: List[int], sample_rate: int):
    """
    打印数据摘要
    """
    print("=" * 60)
    print("RRAM Write Test Trend Analysis Summary")
    print("=" * 60)
    
    print(f"\nData Overview:")
    print(f"  Total data points analyzed: {len(updates):,}")
    print(f"  Sample rate: 1 in {sample_rate}")
    print(f"  Update range: {min(updates)} - {max(updates)}")
    
    print(f"\nRMSE Analysis:")
    rmse_mean = np.mean(rmse_values)
    rmse_std = np.std(rmse_values)
    print(f"  Mean: {rmse_mean:.6f}")
    print(f"  Std Dev: {rmse_std:.6f}")
    print(f"  Range: {min(rmse_values):.6f} - {max(rmse_values):.6f}")
    
    print(f"\nMax Drift Analysis:")
    drift_mean = np.mean(max_drift_values)
    drift_std = np.std(max_drift_values)
    print(f"  Mean: {drift_mean:.6f}")
    print(f"  Std Dev: {drift_std:.6f}")
    print(f"  Range: {min(max_drift_values):.6f} - {max(max_drift_values):.6f}")
    
    print(f"\nClamps Analysis:")
    clamps_mean = np.mean(clamps_values)
    clamps_std = np.std(clamps_values)
    clamps_total = sum(clamps_values) * sample_rate  # 估算总钳位次数
    print(f"  Mean: {clamps_mean:.2f}")
    print(f"  Std Dev: {clamps_std:.2f}")
    print(f"  Estimated total clamps: {clamps_total:,}")
    print(f"  Range: {min(clamps_values)} - {max(clamps_values)}")
    
    # 趋势分析
    print(f"\nTrend Analysis:")
    mid_point = len(updates) // 2
    rmse_first_half = np.mean(rmse_values[:mid_point])
    rmse_second_half = np.mean(rmse_values[mid_point:])
    rmse_trend = "Increasing" if rmse_second_half > rmse_first_half else "Decreasing"
    
    drift_first_half = np.mean(max_drift_values[:mid_point])
    drift_second_half = np.mean(max_drift_values[mid_point:])
    drift_trend = "Increasing" if drift_second_half > drift_first_half else "Decreasing"
    
    print(f"  RMSE overall trend: {rmse_trend} ({rmse_first_half:.6f} → {rmse_second_half:.6f})")
    print(f"  Drift overall trend: {drift_trend} ({drift_first_half:.6f} → {drift_second_half:.6f})")

def main():
    """主函数"""
    filename = 'write_test_100000.log'
    sample_rate = 100  # 每100行取1行，减少数据量
    
    print("Parsing log file with sampling...")
    updates, rmse_values, max_drift_values, clamps_values = parse_log_file_sampled(filename, sample_rate)
    
    print(f"Successfully parsed {len(updates)} data points (sampled from original data)")
    
    print("Generating individual trend plots...")
    create_individual_trend_plots(updates, rmse_values, max_drift_values, clamps_values)
    
    print("Analyzing trends...")
    print_summary(updates, rmse_values, max_drift_values, clamps_values, sample_rate)
    
    print("\nAnalysis complete! Generated files:")
    print("  - rmse_trend.png")
    print("  - max_drift_trend.png") 
    print("  - clamps_trend.png")

if __name__ == "__main__":
    main()
