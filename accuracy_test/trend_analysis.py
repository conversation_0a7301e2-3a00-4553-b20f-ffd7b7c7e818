#!/usr/bin/env python3
"""
RRAM写入测试趋势分析工具
为每类数据创建独立的趋势图
"""

import re
import matplotlib.pyplot as plt
import numpy as np
from typing import List, <PERSON><PERSON>

def parse_log_file(filename: str) -> Tuple[List[int], List[float], List[float], List[int]]:
    """
    解析日志文件，提取数据
    
    Returns:
        updates: 更新次数列表
        rmse_values: RMSE值列表
        max_drift_values: 最大漂移值列表
        clamps_values: 钳位次数列表
    """
    updates = []
    rmse_values = []
    max_drift_values = []
    clamps_values = []
    
    # 正则表达式匹配数据行
    pattern = r'\[Update (\d+)\] RMSE: ([\d.]+), Max drift: ([\d.]+), Clamps: (\d+)'
    
    with open(filename, 'r') as f:
        for line in f:
            match = re.search(pattern, line)
            if match:
                update_num = int(match.group(1))
                rmse = float(match.group(2))
                max_drift = float(match.group(3))
                clamps = int(match.group(4))
                
                updates.append(update_num)
                rmse_values.append(rmse)
                max_drift_values.append(max_drift)
                clamps_values.append(clamps)
    
    return updates, rmse_values, max_drift_values, clamps_values

def create_trend_plots(updates: List[int], rmse_values: List[float],
                      max_drift_values: List[float], clamps_values: List[int]):
    """
    Create three separate trend plots
    """
    # Set font for better compatibility
    plt.rcParams['font.family'] = 'DejaVu Sans'

    # Create three subplots
    fig, axes = plt.subplots(3, 1, figsize=(12, 15))
    fig.suptitle('RRAM Write Test Trend Analysis (100,000 Updates)', fontsize=16, fontweight='bold')

    # 1. RMSE Trend Plot
    axes[0].plot(updates, rmse_values, color='#2E86AB', linewidth=0.8, alpha=0.8)
    axes[0].set_title('RMSE (Root Mean Square Error) Trend', fontsize=14, fontweight='bold', pad=20)
    axes[0].set_xlabel('Update Number', fontsize=12)
    axes[0].set_ylabel('RMSE Value', fontsize=12)
    axes[0].grid(True, alpha=0.3)
    axes[0].set_xlim(0, max(updates))

    # Add statistical information
    rmse_mean = np.mean(rmse_values)
    rmse_std = np.std(rmse_values)
    rmse_max = np.max(rmse_values)
    rmse_min = np.min(rmse_values)

    axes[0].axhline(y=rmse_mean, color='red', linestyle='--', alpha=0.7, label=f'Mean: {rmse_mean:.4f}')
    axes[0].legend(loc='upper right')

    # Add text box with statistics
    stats_text = f'Statistics:\nMean: {rmse_mean:.4f}\nStd: {rmse_std:.4f}\nMax: {rmse_max:.4f}\nMin: {rmse_min:.4f}'
    axes[0].text(0.02, 0.98, stats_text, transform=axes[0].transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 2. Max Drift Trend Plot
    axes[1].plot(updates, max_drift_values, color='#A23B72', linewidth=0.8, alpha=0.8)
    axes[1].set_title('Max Drift Trend', fontsize=14, fontweight='bold', pad=20)
    axes[1].set_xlabel('Update Number', fontsize=12)
    axes[1].set_ylabel('Max Drift Value', fontsize=12)
    axes[1].grid(True, alpha=0.3)
    axes[1].set_xlim(0, max(updates))

    # Add statistical information
    drift_mean = np.mean(max_drift_values)
    drift_std = np.std(max_drift_values)
    drift_max = np.max(max_drift_values)
    drift_min = np.min(max_drift_values)

    axes[1].axhline(y=drift_mean, color='red', linestyle='--', alpha=0.7, label=f'Mean: {drift_mean:.4f}')
    axes[1].legend(loc='upper right')

    stats_text = f'Statistics:\nMean: {drift_mean:.4f}\nStd: {drift_std:.4f}\nMax: {drift_max:.4f}\nMin: {drift_min:.4f}'
    axes[1].text(0.02, 0.98, stats_text, transform=axes[1].transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # 3. Clamps Trend Plot
    axes[2].plot(updates, clamps_values, color='#F18F01', linewidth=0.8, alpha=0.8)
    axes[2].set_title('Clamps Count Trend', fontsize=14, fontweight='bold', pad=20)
    axes[2].set_xlabel('Update Number', fontsize=12)
    axes[2].set_ylabel('Clamps Count', fontsize=12)
    axes[2].grid(True, alpha=0.3)
    axes[2].set_xlim(0, max(updates))

    # Add statistical information
    clamps_mean = np.mean(clamps_values)
    clamps_std = np.std(clamps_values)
    clamps_max = np.max(clamps_values)
    clamps_min = np.min(clamps_values)

    if clamps_mean > 0:
        axes[2].axhline(y=clamps_mean, color='red', linestyle='--', alpha=0.7, label=f'Mean: {clamps_mean:.2f}')
        axes[2].legend(loc='upper right')

    stats_text = f'Statistics:\nMean: {clamps_mean:.2f}\nStd: {clamps_std:.2f}\nMax: {clamps_max}\nMin: {clamps_min}'
    axes[2].text(0.02, 0.98, stats_text, transform=axes[2].transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('rram_trend_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

def analyze_trends(updates: List[int], rmse_values: List[float], 
                  max_drift_values: List[float], clamps_values: List[int]):
    """
    分析趋势并输出关键信息
    """
    print("=" * 60)
    print("RRAM写入测试趋势分析报告")
    print("=" * 60)
    
    print(f"\n数据概览:")
    print(f"  总更新次数: {len(updates):,}")
    print(f"  更新范围: {min(updates)} - {max(updates)}")
    
    print(f"\nRMSE分析:")
    rmse_mean = np.mean(rmse_values)
    rmse_std = np.std(rmse_values)
    print(f"  平均值: {rmse_mean:.6f}")
    print(f"  标准差: {rmse_std:.6f}")
    print(f"  变异系数: {rmse_std/rmse_mean:.2%}")
    print(f"  范围: {min(rmse_values):.6f} - {max(rmse_values):.6f}")
    
    print(f"\n最大漂移分析:")
    drift_mean = np.mean(max_drift_values)
    drift_std = np.std(max_drift_values)
    print(f"  平均值: {drift_mean:.6f}")
    print(f"  标准差: {drift_std:.6f}")
    print(f"  变异系数: {drift_std/drift_mean:.2%}")
    print(f"  范围: {min(max_drift_values):.6f} - {max(max_drift_values):.6f}")
    
    print(f"\n钳位次数分析:")
    clamps_mean = np.mean(clamps_values)
    clamps_std = np.std(clamps_values)
    clamps_total = sum(clamps_values)
    print(f"  平均值: {clamps_mean:.2f}")
    print(f"  标准差: {clamps_std:.2f}")
    print(f"  总钳位次数: {clamps_total}")
    print(f"  钳位频率: {clamps_total/len(updates):.4f} 次/更新")
    print(f"  范围: {min(clamps_values)} - {max(clamps_values)}")
    
    # 趋势分析
    print(f"\n趋势分析:")
    
    # 计算前后半段的平均值比较
    mid_point = len(updates) // 2
    rmse_first_half = np.mean(rmse_values[:mid_point])
    rmse_second_half = np.mean(rmse_values[mid_point:])
    rmse_trend = "上升" if rmse_second_half > rmse_first_half else "下降"
    
    drift_first_half = np.mean(max_drift_values[:mid_point])
    drift_second_half = np.mean(max_drift_values[mid_point:])
    drift_trend = "上升" if drift_second_half > drift_first_half else "下降"
    
    print(f"  RMSE总体趋势: {rmse_trend} ({rmse_first_half:.6f} → {rmse_second_half:.6f})")
    print(f"  漂移总体趋势: {drift_trend} ({drift_first_half:.6f} → {drift_second_half:.6f})")
    
    if clamps_total == 0:
        print(f"  钳位状态: 整个测试过程中未发生钳位")
    else:
        print(f"  钳位状态: 发生了 {clamps_total} 次钳位")

def main():
    """主函数"""
    filename = 'write_test_100000.log'
    
    print("正在解析日志文件...")
    updates, rmse_values, max_drift_values, clamps_values = parse_log_file(filename)
    
    print(f"成功解析 {len(updates)} 条记录")
    
    print("正在生成趋势图...")
    create_trend_plots(updates, rmse_values, max_drift_values, clamps_values)
    
    print("正在分析趋势...")
    analyze_trends(updates, rmse_values, max_drift_values, clamps_values)
    
    print("\n分析完成！趋势图已保存为 'rram_trend_analysis.png'")

if __name__ == "__main__":
    main()
