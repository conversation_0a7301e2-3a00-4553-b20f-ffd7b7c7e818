import os
import sys
import time
import numpy as np
import torch
from collections import defaultdict
from tqdm import tqdm

script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'PIM-Frontend'))

try:
    from pim import wage_quantizer
    from pim.partition import partition_matrix_multiply_with_ADC, get_partition_size
    from pim.pim_modules import QLinear, QAttention
    from pim.modeling_read_disturb import resistance_to_state
    from pim.modeling_read_disturb_HRS import solve_hrs
    from pim.modeling_read_disturb_LRS import solve_lrs
    from mapping_scheduling.configs import slam_factorgraph_ACIM
    print("Successfully imported all required modules.")
except ImportError as e:
    print(f"Fatal Error: A required module could not be imported: {e}")
    print("PIM-Frontend and mapping_scheduling are required!!")
    raise

class PIM_Accuracy_Tracker:
    def __init__(
            self,  
            total_cells=128*128, 
            num_subarrays=1, 
            d2d_variation=0.01, 
            c2c_variation=0.005, 
            non_linearity_ltp=1.75, 
            non_linearity_ltd=1.46, 
            max_level=16, 
            adc_precision=5,
            device='cpu'
        ):

        self.total_cells = total_cells
        self.sub_array_rows = 128
        self.sub_array_cols = 128

        self.d2dVari = d2d_variation
        self.c2cVari = c2c_variation
        self.nonlinearityLTP = non_linearity_ltp
        self.nonlinearityLTD = non_linearity_ltd
        self.max_level = max_level
        self.ADCprecision = adc_precision
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.num_subarrays = max(1, total_cells // (self.sub_array_rows * self.sub_array_cols))
        self.cell_conductances = torch.normal(mean=0.0, std=0.02, size=(total_cells,), device=self.device)  
        self.cell_weights_history = torch.normal(mean=0.0, std=0.02, size=(total_cells,), device=self.device)
        self.cell_conductances.clamp_(-0.9, 0.9)  
        self.cell_weights_history.clamp_(-0.9, 0.9)
        self.write_cycles = np.zeros(total_cells)
        self.read_cycles = np.zeros(total_cells)
        self.operation_log = []
        self.subarray_usage_count = np.zeros(self.num_subarrays)
        self.ACIM_OPERATIONS = {'StaticVMM', 'WriteWeights'}
        
        # Track NaN and clamping issues for debug
        self.nan_count = 0
        self.clamp_count = 0
        self.nan_reset_count = 0

        # # DEBUG: Check initial 
        # print(f"[DEBUG] Initializing PIM RRAM Accuracy Tracker:")
        # print(f"[DEBUG] - Total Cells: {total_cells:,}")
        # print(f"[DEBUG] - Number of Subarrays: {self.num_subarrays}")
        # print(f"[DEBUG] - Device-to-Device Variation: {d2d_variation*100:.1f}%")
        # print(f"[DEBUG] - Cycle-to-Cycle Variation: {c2c_variation*100:.1f}%")
        # print(f"[DEBUG] - Non-linearity Params: LTP={non_linearity_ltp}, LTD={non_linearity_ltd}")
        # print(f"[DEBUG] - ADC Precision: {adc_precision} bits")
        # print(f"[DEBUG] - Computing on Device: {self.device}")
        # print(f"[DEBUG] - Simulating ONLY ACIM Operations: {self.ACIM_OPERATIONS}")
        # print(f"[DEBUG] Initial weight statistics:")
        # print(f"[DEBUG] - Weight mean: {self.cell_weights_history.mean().item():.6f}")
        # print(f"[DEBUG] - Weight std: {self.cell_weights_history.std().item():.6f}")
        # print(f"[DEBUG] - Conductance mean: {self.cell_conductances.mean().item():.6f}")
        # print(f"[DEBUG] - Any NaN in weights: {torch.isnan(self.cell_weights_history).any().item()}")
        # print(f"[DEBUG] - Any NaN in conductances: {torch.isnan(self.cell_conductances).any().item()}")

    def simulate_slam_rram_write_operation(self, cell_ids, target_weights, operation_type):
        if not isinstance(cell_ids, (list, np.ndarray)):
            cell_ids = [cell_ids]
        if not isinstance(target_weights, torch.Tensor):
            target_weights = torch.tensor(target_weights, device=self.device, dtype=torch.float32)

        min_safe_weight = -0.9  
        max_safe_weight = 0.9   
        max_param_change = 0.05

        for i, cell_id in enumerate(cell_ids):
            if cell_id >= self.total_cells:
                raise ValueError(f"Cell ID {cell_id} is out of bounds.")

            current_weight = self.cell_weights_history[cell_id:cell_id+1]
            current_weight.clamp_(min_safe_weight, max_safe_weight)

            target_weight_val = target_weights.flatten()[i] if target_weights.numel() > i else target_weights.flatten()[0]
            target_weight = torch.clamp(target_weight_val, min_safe_weight, max_safe_weight).unsqueeze(0)
            
            param = target_weight - current_weight
            
            current_pos = current_weight.item()
            if abs(current_pos) > 0.7:  
                decay_factor = 0.5  
                param = param * decay_factor
            
            param = torch.clamp(param, -max_param_change, max_param_change)

            # Pre-computation check for NaN
            if torch.isnan(current_weight).any() or torch.isnan(target_weight).any():
                print(f"[NaN Alert] Cell {cell_id}: current={current_weight.item():.6f}, target={target_weight.item():.6f}, param={param.item():.6f}")
                self.nan_count += 1
                continue
                
            # Boundary protection
            if current_weight.item() > 0.8 and param.item() > 0:
                param = param * 0.3   
            elif current_weight.item() < -0.8 and param.item() < 0:
                param = param * 0.3   
            elif abs(current_weight.item()) > 0.6:
                param = param * 0.7   
                
            
            try:
                weight_change = wage_quantizer.NonlinearWeightUpdateWithD2DVariations(
                    origin=current_weight,
                    param=param,
                    d2dVari=self.d2dVari,
                    nonlinearityLTP=self.nonlinearityLTP,
                    nonlinearityLTD=self.nonlinearityLTD,
                    max_level=self.max_level
                )
                
                updated_weight = current_weight - weight_change
                
                if self.c2cVari > 0:
                    updated_weight = wage_quantizer.NonlinearWeightUpdateWithC2CVariations(
                        updated_weight, self.c2cVari
                    )

                # Post-computation check for NaN
                if torch.isnan(updated_weight).any() or torch.isinf(updated_weight).any():
                    self.nan_reset_count += 1
                    print(f"[NaN Alert] Cell {cell_id}: current={current_weight.item():.6f}, param={param.item():.6f}, weight_change={weight_change.item():.6f}, updated={updated_weight.item() if not torch.isnan(updated_weight).any() else 'NaN'}")
                    updated_weight = torch.tensor([0.0], device=self.device)  
                    print(f"[NaN Reset] Cell {cell_id} reset to 0.0. Total resets: {self.nan_reset_count}")

                # Ensure stay within the desired physical range
                final_weight = torch.clamp(updated_weight, min_safe_weight, max_safe_weight)
                if not torch.equal(final_weight, updated_weight):
                    self.clamp_count += 1
                    # [Clamp]
                    if self.clamp_count % 100 == 0: 
                        print(f"[Clamp] Cell {cell_id}: {updated_weight.item():.6f} -> {final_weight.item():.6f} "
                              f"(Total clamps: {self.clamp_count})")
                
                self.cell_weights_history[cell_id] = final_weight.item()
                self.cell_conductances[cell_id] = final_weight.item() 
                self.write_cycles[cell_id] += 1
                subarray_id = self.get_subarray_id(cell_id)
                self.subarray_usage_count[subarray_id] += 1
                
            except Exception as e:
                raise ValueError(f"Error in PIM-Frontend call for cell {cell_id}: {e}")

    def simulate_slam_rram_read_operation(self, cell_ids, operation_type):
        if not isinstance(cell_ids, (list, np.ndarray)):
            cell_ids = [cell_ids]

        read_values = []
        for cell_id in cell_ids:
            if cell_id >= self.total_cells:
                raise ValueError(f"Cell ID {cell_id} is out of bounds.")
            
            current_conductance = self.cell_conductances[cell_id]
            read_values.append(current_conductance.item())
            self.read_cycles[cell_id] += 1

        if read_values:
            read_tensor = torch.tensor(read_values, device=self.device, dtype=torch.float32)
            quantized_values = wage_quantizer.LinearQuantizeOut(read_tensor, self.ADCprecision)
            return quantized_values.cpu().numpy().tolist()

        return []

    def get_subarray_id(self, cell_id):
        cells_per_subarray = self.sub_array_rows * self.sub_array_cols
        return min(cell_id // cells_per_subarray, self.num_subarrays - 1)

    def simulate_slam_matrix_operation(self, operation_type, matrix_size, enable_partition=True, operation_cycles=1):
        if operation_type not in self.ACIM_OPERATIONS:
            return
        rows, cols = matrix_size
        total_elements = rows * cols

        cells_per_subarray = self.sub_array_rows * self.sub_array_cols
        num_required_subarrays = (total_elements + cells_per_subarray - 1) // cells_per_subarray
        
        if self.num_subarrays < num_required_subarrays:
            raise ValueError(
                f"Not enough cells for this operation: {self.num_subarrays} subarrays available, "
                f"{num_required_subarrays} required for {total_elements} elements."
            )

        start_subarray = np.random.randint(0, self.num_subarrays - num_required_subarrays + 1)
        optimal_start = start_subarray * cells_per_subarray
        cell_ids = list(range(optimal_start, optimal_start + total_elements))

        if operation_type == 'WriteWeights':
            current_avg_weight = self.cell_weights_history[cell_ids].mean().item()
            weight_variation = 0.08  
            weight_data = torch.normal(
                mean=current_avg_weight * 0.5,  
                std=weight_variation, 
                size=(total_elements,), 
                device=self.device
            )
            weight_data = torch.clamp(weight_data, -0.8, 0.8)  # Aligned with PIM-Frontend range
            # print(f"[DEBUG] weight_data: {weight_data}")

            if enable_partition:
                partition_size = min(cells_per_subarray, total_elements)
                for i in range(0, total_elements, partition_size):
                    batch_cells = cell_ids[i:i+partition_size]
                    batch_data = weight_data[i:i+partition_size]
                    self.simulate_slam_rram_write_operation(batch_cells, batch_data, operation_type)
            else:
                self.simulate_slam_rram_write_operation(cell_ids, weight_data, operation_type)

        elif operation_type == 'StaticVMM':
            read_cycles = operation_cycles
            if enable_partition:
                partition_size = self.sub_array_rows
                for _ in range(read_cycles):
                    for i in range(0, total_elements, partition_size):
                        batch_cells = cell_ids[i:i+partition_size]
                        self.simulate_slam_rram_read_operation(batch_cells, operation_type)
            else:
                for _ in range(read_cycles):
                    self.simulate_slam_rram_read_operation(cell_ids, operation_type)

        self.operation_log.append({
            'operation': operation_type,
            'matrix_size': matrix_size,
            'affected_cells': len(cell_ids),
            'partition_enabled': enable_partition,
            'operation_cycles': operation_cycles,
            'timestamp': len(self.operation_log)
        })

    def get_comprehensive_statistics(self):
        if not self.operation_log:
            return {
                'total_operations': 0, 
                'avg_write_cycles': 0, 
                'max_write_cycles': 0,
                'avg_read_cycles': 0, 
                'max_read_cycles': 0
            }

        basic_stats = {
            'total_operations': len(self.operation_log),
            'avg_write_cycles': float(np.mean(self.write_cycles)) if len(self.write_cycles) > 0 else 0,
            'max_write_cycles': int(np.max(self.write_cycles)) if len(self.write_cycles) > 0 else 0,
            'avg_read_cycles': float(np.mean(self.read_cycles)) if len(self.read_cycles) > 0 else 0,
            'max_read_cycles': int(np.max(self.read_cycles)) if len(self.read_cycles) > 0 else 0,
            'nan_count': self.nan_count,
            'clamp_count': self.clamp_count,
            'nan_reset_count': self.nan_reset_count,
        }

        pim_stats = {
            'weight_mean': float(self.cell_weights_history.mean().item()),
            'weight_std': float(self.cell_weights_history.std().item()),
            'weight_min': float(self.cell_weights_history.min().item()),
            'weight_max': float(self.cell_weights_history.max().item()),
            'conductance_mean': float(self.cell_conductances.mean().item()),
            'conductance_std': float(self.cell_conductances.std().item()),
            'subarray_usage_variance': float(np.var(self.subarray_usage_count)),
            'subarray_usage_count': self.subarray_usage_count,
            'device_utilization': str(self.device),
        }

        slam_stats = {}
        op_type_counts = defaultdict(int)
        for log in self.operation_log:
            op_type_counts[log['operation']] += 1
        
        slam_stats['operation_distribution'] = dict(op_type_counts)
        
        if self.operation_log:
            slam_stats['avg_matrix_size'] = np.mean([
                log['matrix_size'][0] * log['matrix_size'][1]
                for log in self.operation_log
            ])
        else:
            slam_stats['avg_matrix_size'] = 0

        return {**basic_stats, **pim_stats, **slam_stats}

def run_slam_pim_accuracy_test():
    print("=====Starting RRAM Accuracy Test=====")

    tracker = PIM_Accuracy_Tracker(
        total_cells=16384,              
        d2d_variation=0.01,             # More realistic 1% d2d variation
        c2c_variation=0.003,            # PIM-Frontend default 0.3%
        non_linearity_ltp=1.75,         
        non_linearity_ltd=1.46,         
    )

    ops_SLAM = []
    try:
        _, ops_SLAM_raw, _, _ = slam_factorgraph_ACIM.get_scheduling_configs()
        ops_SLAM = ops_SLAM_raw
        print(f"Generated {len(ops_SLAM)} real SLAM operations.")
    except Exception as e:
        raise ValueError(f"SLAM operation are required: {str(e)}")

    test_cycles = 200  # Reduced for faster testing - TIME
    print(f"Starting optimized simulation for {test_cycles} cycles...")
    print("Anti-clamp optimizations enabled - monitoring for improved stability")
    start_time = time.time()
    
    acim_ops_executed = 0
    # End of Functional Life Cycle detection
    end_of_life_cycle = None
    EOFL_THRESHOLD = 0.25  # 25% clamp rate threshold - more realistic
    last_report_clamp_count = 0
    last_report_write_ops = 0

    for cycle in tqdm(range(test_cycles), desc="Simulating SLAM Cycles"):
        for op in ops_SLAM:
            if hasattr(op, 'type') and hasattr(op, 'data_size'):
                if op.type not in tracker.ACIM_OPERATIONS:
                    continue
                
                acim_ops_executed += 1
                matrix_size = op.data_size if isinstance(op.data_size, tuple) else (64, 64)
                
                operation_cycles = 1
                if op.type == 'StaticVMM':
                    operation_cycles = 3
                
                tracker.simulate_slam_matrix_operation(
                    operation_type=op.type,
                    matrix_size=matrix_size,
                    enable_partition=True,
                    operation_cycles=operation_cycles
                )

        if (cycle + 1) % 20 == 0 or cycle == test_cycles - 1:
            stats = tracker.get_comprehensive_statistics()
            if stats and stats['total_operations'] > 0:
                
                # Calculate and check Clamp Rate
                current_total_writes = np.sum(tracker.write_cycles)
                writes_in_period = current_total_writes - last_report_write_ops
                clamps_in_period = stats['clamp_count'] - last_report_clamp_count
                
                clamp_rate_in_period = (clamps_in_period / writes_in_period) if writes_in_period > 0 else 0

                if clamp_rate_in_period > EOFL_THRESHOLD and end_of_life_cycle is None:
                    end_of_life_cycle = cycle + 1
                    e_msg = (
                        f"\n{'*'*60}\n"
                        f"[!!!] End of Functional Life (EoFL) Threshold Reached at Cycle {end_of_life_cycle} [!!!]\n"
                        f"      Clamp rate in this period ({clamp_rate_in_period:.2%}) exceeded the {EOFL_THRESHOLD:.0%} threshold.\n"
                        f"{'*'*60}"
                    )
                    print(e_msg)

                last_report_clamp_count = stats['clamp_count']
                last_report_write_ops = current_total_writes

                print(f"\n=== Cycle {cycle + 1}/{test_cycles} Progress Report ===")
                print(f"Operations: {stats['total_operations']}")
                print(f"Weight Mean: {stats['weight_mean']:.6f}")
                print(f"Weight Std: {stats['weight_std']:.6f}")
                print(f"Weight Range: [{stats['weight_min']:.6f}, {stats['weight_max']:.6f}]")
                print(f"NaN Resets: {stats['nan_reset_count']}")
                print(f"Clamps: {stats['clamp_count']} (Period Rate: {clamp_rate_in_period:.2%})")
                print(f"Write Cycles (avg): {stats['avg_write_cycles']:.2f}")
                print(f"Read Cycles (avg): {stats['avg_read_cycles']:.2f}")
                
                nan_weights = torch.isnan(tracker.cell_weights_history).sum().item()
                nan_conductances = torch.isnan(tracker.cell_conductances).sum().item()
                if nan_weights > 0 or nan_conductances > 0:
                    print(f"[WARNING] Current NaN count - Weights: {nan_weights}, Conductances: {nan_conductances}")
                else:
                    print(f"[OK] No NaN values detected in current arrays")
                print("="*40)

    test_duration = time.time() - start_time
    print(f"Physical modeling simulation finished in: {test_duration:.2f} seconds")
    print(f"Total ACIM operations executed: {acim_ops_executed}")
    
    final_stats = tracker.get_comprehensive_statistics()
    final_stats['end_of_life_cycle'] = end_of_life_cycle
    return final_stats


def print_comprehensive_statistics(stats):
    """Prints a comprehensive summary of the accuracy test results."""
    if not stats or stats['total_operations'] == 0:
        print("\nNo ACIM operations were simulated. Final statistics are empty.")
        return

    def print_header(title):
        print("\n" + "="*60)
        print(f"{title}")
        print("="*60)

    print_header("RRAM Accuracy Test Report")

    print("\n## Test Summary")
    print(f"- Total ACIM Operations Simulated: {stats['total_operations']:,}")
    print(f"- Average Write Cycles per Cell: {stats['avg_write_cycles']:.2f}")
    print(f"- Average Read Cycles per Cell: {stats['avg_read_cycles']:.2f}")

    print("\n## PIM-Frontend Direct Results (Accuracy)")
    print("- Weight Statistics:")
    print(f"  - Mean: {stats['weight_mean']:.6f}")
    print(f"  - Standard Deviation: {stats['weight_std']:.6f}")
    print(f"  - Range: [{stats['weight_min']:.6f}, {stats['weight_max']:.6f}]")
    print("- Conductance Statistics:")
    print(f"  - Mean: {stats['conductance_mean']:.6f}")
    print(f"  - Standard Deviation: {stats['conductance_std']:.6f}")
    print(f"- Subarray Usage Variance: {stats['subarray_usage_variance']:.6f}")

    print("\n## Robustness & Stability")
    print(f"- Values Clamped to Safe Range: {stats['clamp_count']:,} times")
    print(f"- NaN/Inf Values Detected & Reset: {stats['nan_reset_count']:,} times")
    
    if 'end_of_life_cycle' in stats and stats['end_of_life_cycle'] is not None:
        print(f"- [IMPORTANT] Functional Lifetime Threshold (25% clamp rate) was reached at CYCLE: {stats['end_of_life_cycle']}")
    else:
        print("- [IMPORTANT] Functional Lifetime Threshold was NOT reached within the simulation time.")

    # Final NaN check
    if 'weight_mean' in stats and 'conductance_mean' in stats:
        if np.isnan(stats['weight_mean']) or np.isnan(stats['conductance_mean']):
            print("- [CRITICAL] Final statistics contain NaN values!")
        else:
            print("- [OK] Final statistics are numerically clean")
    
    # Calculate clamp and NaN rates
    if stats['total_operations'] > 0:
        clamp_rate = (stats['clamp_count'] / stats['total_operations']) * 100
        nan_rate = (stats['nan_reset_count'] / stats['total_operations']) * 100
        print(f"- Clamp Rate: {clamp_rate:.2f}% of operations")
        print(f"- NaN Rate: {nan_rate:.3f}% of operations")

    if 'operation_distribution' in stats and stats['total_operations'] > 0:
        print("\n## SLAM Workload Characteristics (ACIM Operations Only)")
        print("\n### Operation Type Distribution")
        total_ops = stats['total_operations']
        for op_type, count in stats['operation_distribution'].items():
            percentage = (count / total_ops) * 100 if total_ops > 0 else 0
            print(f"- {op_type:<15}: {count:>7,} operations ({percentage:.2f}%)")

        print("\n### Subarray Operation Distribution")
        # Find which subarrays were actually used
        used_subarrays = np.where(stats['subarray_usage_count'] > 0)[0]
        for subarray_id in used_subarrays:
            print(f"- Subarray {subarray_id}: {stats['subarray_usage_count'][subarray_id]:,} operations")


def main():
    final_stats = run_slam_pim_accuracy_test()
    print_comprehensive_statistics(final_stats)


if __name__ == "__main__":
    main()
