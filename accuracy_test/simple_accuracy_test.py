import os
import sys
import numpy as np
import torch

# Add project paths
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'PIM-Frontend'))

try:
    from pim import wage_quantizer
    from comprehensive_accuracy_evaluator import ComprehensiveAccuracyEvaluator, AccuracyMetrics
    from reference_slam_benchmark import ReferenceSLAMBenchmark
    print("Successfully imported accuracy evaluation modules.")
except ImportError as e:
    print(f"Error importing modules: {e}")
    raise

def test_simple_vmm_accuracy():
    """
    Test simple VMM operations for accuracy evaluation
    """
    print("="*60)
    print("Simple VMM Accuracy Test")
    print("="*60)
    
    # Initialize evaluator and benchmark
    evaluator = ComprehensiveAccuracyEvaluator()
    benchmark = ReferenceSLAMBenchmark(precision='double')
    
    # Test parameters
    num_tests = 10
    matrix_sizes = [(4, 1), (8, 1), (16, 1)]
    
    print(f"Running {num_tests} tests for each matrix size...")
    
    for size_idx, (rows, cols) in enumerate(matrix_sizes):
        print(f"\nTesting {rows}x{cols} matrices:")
        
        for test_idx in range(num_tests):
            # Generate test data
            weight_matrix = torch.randn(rows, cols, dtype=torch.float64) * 0.1
            input_vector = torch.randn(cols, 1, dtype=torch.float64) * 0.1
            
            # Generate reference result
            ref_result = benchmark.reference_static_vmm(weight_matrix, input_vector)
            
            # Simulate RRAM computation (with some noise)
            rram_weights = weight_matrix.float() + torch.randn_like(weight_matrix.float()) * 0.001  # Add small noise
            rram_input = input_vector.float()
            rram_result = torch.matmul(rram_weights, rram_input)
            
            # Apply ADC quantization (simulate RRAM ADC)
            rram_result_quantized = wage_quantizer.LinearQuantizeOut(rram_result.flatten(), 5).reshape(rram_result.shape)
            
            # Record reference result first
            operation_key = f"VMM_{size_idx}_{test_idx}"
            evaluator.reference_results[operation_key] = {
                'result': ref_result.result,
                'input': input_vector,
                'operation_type': 'StaticVMM',
                'matrix_size': (rows, cols),
                'timestamp': ref_result.computation_time
            }

            # Then record RRAM result
            evaluator.record_rram_operation_result(
                operation_key=operation_key,
                rram_result=rram_result_quantized,
                operation_metadata={
                    'operation_type': 'StaticVMM',
                    'matrix_size': (rows, cols),
                    'test_index': test_idx
                }
            )
            
            print(f"  Test {test_idx+1}: Ref={ref_result.result.norm().item():.6f}, "
                  f"RRAM={rram_result_quantized.norm().item():.6f}")
    
    # Generate some weight history for stability analysis
    weight_history = []
    error_sequence = []
    
    for i in range(20):
        # Simulate weight evolution
        weights = torch.randn(100, dtype=torch.float32) * 0.1
        if i > 0:
            # Add some drift
            weights = weight_history[-1] + torch.randn(100, dtype=torch.float32) * 0.001
        weight_history.append(weights)
        
        # Simulate error evolution (decreasing)
        error = 1.0 * np.exp(-i * 0.1) + np.random.normal(0, 0.01)
        error_sequence.append(error)
    
    # Evaluate comprehensive accuracy
    print("\n" + "="*60)
    print("Computing Comprehensive Accuracy Metrics...")
    print("="*60)
    
    try:
        metrics = evaluator.evaluate_comprehensive_accuracy(
            weight_history=weight_history,
            error_sequence=error_sequence,
            nan_count=0,
            clamp_count=2,
            total_operations=len(matrix_sizes) * num_tests
        )
        
        # Generate report
        report = evaluator.generate_accuracy_report(metrics, "Simple VMM Accuracy Test")
        print(report)
        
        # Print benchmark summary
        benchmark_summary = benchmark.benchmark_summary()
        print(f"\nBenchmark Summary:")
        print(f"- Total Reference Operations: {benchmark_summary['total_operations']}")
        print(f"- Average Computation Time: {benchmark_summary.get('average_computation_time', 0):.6f}s")
        print(f"- Precision Used: {benchmark_summary['precision_used']}")
        
        return metrics
        
    except Exception as e:
        print(f"Error in accuracy evaluation: {e}")
        return None

def test_weight_update_accuracy():
    """
    Test weight update operations for accuracy evaluation
    """
    print("\n" + "="*60)
    print("Weight Update Accuracy Test")
    print("="*60)
    
    # Initialize
    evaluator = ComprehensiveAccuracyEvaluator()
    benchmark = ReferenceSLAMBenchmark(precision='double')
    
    num_tests = 5
    weight_sizes = [10, 20, 50]
    
    for size in weight_sizes:
        print(f"\nTesting weight updates for {size} weights:")
        
        for test_idx in range(num_tests):
            # Generate test data
            current_weights = torch.randn(size, dtype=torch.float64) * 0.1
            weight_gradients = torch.randn(size, dtype=torch.float64) * 0.01
            
            # Generate reference result
            ref_result = benchmark.reference_weight_update(
                current_weights=current_weights,
                weight_gradients=weight_gradients,
                learning_rate=0.01
            )
            
            # Simulate RRAM weight update with non-linearity
            rram_current = current_weights.float()
            rram_gradients = weight_gradients.float()
            
            # Apply RRAM non-linearity (simplified)
            param = -0.01 * rram_gradients  # Negative because it's weight change
            
            # Use PIM-Frontend function for realistic simulation
            try:
                weight_change = wage_quantizer.NonlinearWeightUpdateWithD2DVariations(
                    origin=rram_current.unsqueeze(0),
                    param=param.unsqueeze(0),
                    d2dVari=0.01,
                    nonlinearityLTP=1.75,
                    nonlinearityLTD=1.46,
                    max_level=16
                )
                rram_result = rram_current.unsqueeze(0) - weight_change
                rram_result = rram_result.squeeze(0)
            except:
                # Fallback to simple update
                rram_result = rram_current - 0.01 * rram_gradients
            
            # Apply clipping
            rram_result = torch.clamp(rram_result, -0.9, 0.9)
            
            # Record reference result first
            operation_key = f"WeightUpdate_{size}_{test_idx}"
            evaluator.reference_results[operation_key] = {
                'result': ref_result.result,
                'input': weight_gradients,
                'operation_type': 'WriteWeights',
                'matrix_size': (size, 1),
                'timestamp': ref_result.computation_time
            }

            # Then record RRAM result
            evaluator.record_rram_operation_result(
                operation_key=operation_key,
                rram_result=rram_result,
                operation_metadata={
                    'operation_type': 'WriteWeights',
                    'weight_size': size,
                    'test_index': test_idx
                }
            )
            
            ref_norm = ref_result.result.norm().item()
            rram_norm = rram_result.norm().item()
            error = abs(ref_norm - rram_norm) / max(ref_norm, 1e-8) * 100
            
            print(f"  Test {test_idx+1}: Ref_norm={ref_norm:.6f}, "
                  f"RRAM_norm={rram_norm:.6f}, Error={error:.2f}%")
    
    print("\nWeight update accuracy test completed.")

def main():
    """
    Run simple accuracy tests
    """
    print("Starting Simple RRAM Accuracy Tests...")
    
    # Test VMM accuracy
    vmm_metrics = test_simple_vmm_accuracy()
    
    # Test weight update accuracy
    test_weight_update_accuracy()
    
    if vmm_metrics:
        print(f"\n" + "="*60)
        print("SUMMARY")
        print("="*60)
        print(f"Overall Accuracy Score: {vmm_metrics.overall_accuracy_score:.1f}/100")
        print(f"RMSE: {vmm_metrics.rmse:.2e}")
        print(f"Relative Error: {vmm_metrics.relative_error_percent:.2f}%")
        print(f"Clamp Rate: {vmm_metrics.clamp_rate:.2%}")
        print(f"Numerical Range Preservation: {vmm_metrics.numerical_range_preservation:.2%}")
        
        if vmm_metrics.overall_accuracy_score >= 75:
            print("✅ GOOD: Accuracy evaluation framework is working correctly!")
        elif vmm_metrics.overall_accuracy_score >= 50:
            print("⚠️  MODERATE: Framework working but may need tuning")
        else:
            print("❌ POOR: Framework needs debugging")
    
    print("\nSimple accuracy tests completed.")

if __name__ == "__main__":
    main()
