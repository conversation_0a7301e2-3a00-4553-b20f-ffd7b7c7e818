#!/bin/bash
#SBATCH -o hpc_log/job.%j.out
#SBATCH --partition=i64m512u
#SBATCH -J pytorch
#SBATCH -N 1
#SBATCH --ntasks-per-node=2

# i64m512u
# i96m3tu

source /hpc2ssd/softwares/anaconda3/bin/activate base
source setup_env.sh

cfg=islped_exp/fbreakdown-16/config-rbrINR-deit-tiny.yaml

echo "--row-by-row"
python3 execution_and_estimation.py --config $cfg --module-layout islped_exp/ppa/hybrid-deit-tiny-4blocks1circle-168ACIM.txt --exp-tag "$(echo $cfg | tr '/' '-')"

echo "all done!!"