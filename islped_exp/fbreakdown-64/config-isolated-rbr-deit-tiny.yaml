---
simulator:
  booksim_config: ./plugins/booksim2/runfiles/mesh_express.cfg # required config file for booksim
  scheduling_tag: auto_scheduling
  interval_to_report_cycles: 3000
  enable_inr: False
  inr_adder_num: 2
  inr_opbuf_width_b: 1032
  inr_opbuf_depth_b: 4
  record_instr_info: False
  noc_estimator: booksim
  data_flow_fashion: row_by_row
  enable_node_state_monitor: False
  monitor_when_state_changes: True
  monitor_by_cycles: 20
  monitor_verbose: False
---
schedule:
  scheduling_config: attention_fixed
  scheduling_setting:
    module_for_StaticVMM: acim_l
    module_for_DynamicVMM: dcim
  input_row_parallel_dict:
    q_projection: 3
    k_projection: 3
    v_projection: 3
    QxK: 5
    AxV: 9
    out_projection: 1
    ffn0: 1
    ffn1: 1
  model_param: 
    sequence_length: 64
    embedding_dimension: 192
    mlp_size: 768
    heads: 3
    layers: 1
---
arch:
  FREQUENCY: 2e8
  TECHNOLOGY: 22
  WEIGHT_BITS: 8
  INPUT_BITS: 8
  uniform_site_shape: True
  chip_type: flat_noc
---
acim_b:
  ADC_RESOLUTION: 7
  BITS_PER_CELL: 2
  COLUMNS_PER_CROSSBAR: 512
  CROSSBAR_COLS_PER_PE: 16
  CROSSBAR_ROWS_PER_PE: 16
  Num_Col_Muxed: 2
  ROWS_PER_CROSSBAR: 512
  CELL_TYPE: RRAM
---
acim_l:
  ADC_RESOLUTION: 7
  BITS_PER_CELL: 2
  COLUMNS_PER_CROSSBAR: 128
  ROWS_PER_CROSSBAR: 128
  CROSSBAR_COLS_PER_PE: 1
  CROSSBAR_ROWS_PER_PE: 1
  Num_Col_Muxed: 8
  CELL_TYPE: RRAM
---
dcim:
  COLUMNS_PER_CROSSBAR: 256
  CROSSBAR_COLS_PER_PE: 1
  CROSSBAR_ROWS_PER_PE: 1
  ROWS_PER_CROSSBAR: 128
---
mu:
  Mem_Depth: 64
  Mem_Read_Latency: 1
  Mem_Width: 128
  Mem_Write_Latency: 1
---
nu:
  Num_Scalar_Reg: 8
  Vector_Size: 16
---
noc:
  message_buffer_size: 32 # 0 indiactes infinite
  message_size: 128 # size of a message, default is 256 bytes, 0 means treat the whole chunk of gradients as a message
  channel_width: 128 # flit size, # bit, default is 128 bits