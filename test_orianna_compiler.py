"""
Test Script for Orianna Compiler

This script tests the Orianna compiler implementation and its integration
with the Event-Driven-Simulator framework.
"""

import sys
import os
import traceback

# Add orianna_compiler to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from orianna_compiler.compiler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from orianna_compiler.integration.eds_interface import OriannaEDSInterface, compile_slam_for_eds


def test_basic_compilation():
    """Test basic compilation functionality"""
    print("Testing Basic Compilation")
    print("-" * 30)
    
    try:
        # Create compiler
        compiler = OriannaCompiler(enable_logging=True)
        
        # Create simple factor graph
        simple_graph = {
            'variables': [
                {
                    'key': 'x0',
                    'type': 'pose_2d',
                    'dimension': 3,
                    'initial_value': [0.0, 0.0, 0.0]
                },
                {
                    'key': 'x1',
                    'type': 'pose_2d',
                    'dimension': 3,
                    'initial_value': [1.0, 0.0, 0.0]
                }
            ],
            'factors': [
                {
                    'id': 'prior_x0',
                    'type': 'prior',
                    'variables': ['x0'],
                    'measurement': [0.0, 0.0, 0.0],
                    'noise_model': [[0.1, 0.0, 0.0], [0.0, 0.1, 0.0], [0.0, 0.0, 0.1]]
                },
                {
                    'id': 'odom_x0_x1',
                    'type': 'between',
                    'variables': ['x0', 'x1'],
                    'measurement': [1.0, 0.0, 0.0],
                    'noise_model': [[0.2, 0.0, 0.0], [0.0, 0.2, 0.0], [0.0, 0.0, 0.1]]
                }
            ]
        }
        
        # Compile
        result = compiler.compile(simple_graph)
        
        if result.success:
            print("✓ Basic compilation successful!")
            print(f"  Generated {len(result.inference_instructions)} instructions")
            print(f"  Compilation time: {result.compilation_time:.3f}s")
            return True
        else:
            print(f"✗ Basic compilation failed: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"✗ Basic compilation test failed with exception: {e}")
        traceback.print_exc()
        return False


def test_eds_integration():
    """Test EDS integration functionality"""
    print("\nTesting EDS Integration")
    print("-" * 30)
    
    try:
        # Create EDS interface
        eds_interface = OriannaEDSInterface()
        
        # Create factor graph
        slam_graph = {
            'variables': [
                {'key': 'x0', 'type': 'pose_2d', 'dimension': 3, 'initial_value': [0.0, 0.0, 0.0]},
                {'key': 'x1', 'type': 'pose_2d', 'dimension': 3, 'initial_value': [1.0, 0.0, 0.0]},
                {'key': 'l0', 'type': 'point_2d', 'dimension': 2, 'initial_value': [1.5, 1.5]}
            ],
            'factors': [
                {
                    'id': 'prior_x0', 'type': 'prior', 'variables': ['x0'],
                    'measurement': [0.0, 0.0, 0.0],
                    'noise_model': [[0.1, 0.0, 0.0], [0.0, 0.1, 0.0], [0.0, 0.0, 0.1]]
                },
                {
                    'id': 'odom_x0_x1', 'type': 'between', 'variables': ['x0', 'x1'],
                    'measurement': [1.0, 0.0, 0.0],
                    'noise_model': [[0.2, 0.0, 0.0], [0.0, 0.2, 0.0], [0.0, 0.0, 0.1]]
                },
                {
                    'id': 'obs_x1_l0', 'type': 'bearing_range', 'variables': ['x1', 'l0'],
                    'measurement': [0.7071, 1.4142],
                    'noise_model': [[0.1, 0.0], [0.0, 0.1]]
                }
            ]
        }
        
        # Test EDS compilation
        eds_config = eds_interface.compile_slam_factor_graph(slam_graph)
        
        print("✓ EDS integration successful!")
        print(f"  Variables: {eds_config['param']['num_variables']}")
        print(f"  Factors: {eds_config['param']['num_factors']}")
        print(f"  Sub-lists generated: {len(eds_config['sub_lists'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ EDS integration test failed: {e}")
        traceback.print_exc()
        return False


def test_individual_stages():
    """Test individual compilation stages"""
    print("\nTesting Individual Stages")
    print("-" * 30)
    
    try:
        compiler = OriannaCompiler(enable_logging=False)
        
        # Simple test graph
        test_graph = {
            'variables': [
                {'key': 'x0', 'type': 'pose_2d', 'dimension': 3, 'initial_value': [0.0, 0.0, 0.0]}
            ],
            'factors': [
                {
                    'id': 'prior_x0', 'type': 'prior', 'variables': ['x0'],
                    'measurement': [0.0, 0.0, 0.0],
                    'noise_model': [[0.1, 0.0, 0.0], [0.0, 0.1, 0.0], [0.0, 0.0, 0.1]]
                }
            ]
        }
        
        # Test Stage 1: Code Parsing
        print("  Stage 1: Code Parsing...", end=" ")
        factor_graph = compiler.code_parser.parse(test_graph)
        print("✓")
        
        # Test Stage 2: MO-DFG Generation
        print("  Stage 2: MO-DFG Generation...", end=" ")
        mo_dfgs = compiler.mo_dfg_generator.generate(factor_graph)
        print("✓")
        
        # Test Stage 3: Forward Traversal
        print("  Stage 3: Forward Traversal...", end=" ")
        forward_instructions = compiler.forward_traversal.generate_rhs_instructions(factor_graph, mo_dfgs)
        print("✓")
        
        # Test Stage 4: Backward Propagation
        print("  Stage 4: Backward Propagation...", end=" ")
        backward_instructions = compiler.backward_propagation.generate_coefficient_matrix_instructions(factor_graph, mo_dfgs)
        print("✓")
        
        # Test Stage 5: Inference Generation
        print("  Stage 5: Inference Generation...", end=" ")
        inference_instructions = compiler.inference_generator.generate_inference_instructions(
            factor_graph, mo_dfgs, forward_instructions, backward_instructions
        )
        print("✓")
        
        print("✓ All individual stages passed!")
        return True
        
    except Exception as e:
        print(f"\n✗ Individual stages test failed: {e}")
        traceback.print_exc()
        return False


def test_convenience_functions():
    """Test convenience functions"""
    print("\nTesting Convenience Functions")
    print("-" * 30)
    
    try:
        # Test direct EDS compilation
        simple_graph = {
            'variables': [
                {'key': 'x0', 'type': 'pose_2d', 'dimension': 3, 'initial_value': [0.0, 0.0, 0.0]}
            ],
            'factors': [
                {
                    'id': 'prior_x0', 'type': 'prior', 'variables': ['x0'],
                    'measurement': [0.0, 0.0, 0.0],
                    'noise_model': [[0.1, 0.0, 0.0], [0.0, 0.1, 0.0], [0.0, 0.0, 0.1]]
                }
            ]
        }
        
        print("  Testing compile_slam_for_eds...", end=" ")
        eds_config = compile_slam_for_eds(simple_graph)
        print("✓")
        
        print("  Testing example creation...", end=" ")
        compiler = OriannaCompiler()
        example_result = compiler.create_example_slam_compilation()
        if example_result.success:
            print("✓")
        else:
            print(f"✗ ({example_result.error_message})")
            return False
        
        print("✓ Convenience functions passed!")
        return True
        
    except Exception as e:
        print(f"\n✗ Convenience functions test failed: {e}")
        traceback.print_exc()
        return False


def test_error_handling():
    """Test error handling"""
    print("\nTesting Error Handling")
    print("-" * 30)
    
    try:
        compiler = OriannaCompiler(enable_logging=False)
        
        # Test invalid factor graph
        print("  Testing invalid factor graph...", end=" ")
        invalid_graph = {
            'variables': [],  # No variables
            'factors': [
                {
                    'id': 'invalid_factor', 'type': 'prior', 'variables': ['nonexistent'],
                    'measurement': [0.0], 'noise_model': [[0.1]]
                }
            ]
        }
        
        result = compiler.compile(invalid_graph)
        if not result.success:
            print("✓ (correctly failed)")
        else:
            print("✗ (should have failed)")
            return False
        
        # Test empty factor graph
        print("  Testing empty factor graph...", end=" ")
        empty_graph = {'variables': [], 'factors': []}
        result = compiler.compile(empty_graph)
        if not result.success:
            print("✓ (correctly failed)")
        else:
            print("✓ (handled empty graph)")
        
        print("✓ Error handling passed!")
        return True
        
    except Exception as e:
        print(f"\n✗ Error handling test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("Orianna Compiler Test Suite")
    print("=" * 40)
    
    tests = [
        test_basic_compilation,
        test_individual_stages,
        test_eds_integration,
        test_convenience_functions,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test {test.__name__} failed with exception: {e}")
            traceback.print_exc()
    
    print(f"\nTest Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Orianna compiler is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
