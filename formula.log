🚀 修正的线性方程系统构建过程分析
================================================================================
基于论文Figure 10, 11和Equation (4)的正确理解:
1. 误差计算 → 右侧向量 b (前向遍历)
2. Hessian构建 → 左侧矩阵 H (反向遍历)
3. 反向传播写逻辑: 基于输出内容而非节点类型
4. 统一位姿表示和实际变量维度验证

📊 VMM和向量矩阵乘操作统计:
============================================================
    因子图VMM/向量操作写入统计 (localization):
      prior          : VMM       90 + 向量     60 =      150
      imu            : VMM    1,134 + 向量    567 =    1,701
      gps            : VMM      378 + 向量    126 =      504
      camera_lidar   : VMM    2,565 + 向量  1,140 =    3,705
      VMM总计: 4,167 -> 2,916 (DCIM优化)
      向量总计: 1,893
      因子图总计: 4,809
robot       _localization: VMM写入    4,809
    因子图VMM/向量操作写入统计 (planning):
      smooth         : VMM      720 + 向量    240 =      960
      collision_free : VMM    1,080 + 向量    270 =    1,350
      kinematics_dynamics: VMM    1,620 + 向量    360 =    1,980
      VMM总计: 3,420 -> 2,394 (DCIM优化)
      向量总计: 870
      因子图总计: 3,264
robot       _planning    : VMM写入    3,264
    因子图VMM/向量操作写入统计 (control):
      kinematics_dynamics: VMM      972 + 向量    432 =    1,404
      smooth         : VMM      108 + 向量     72 =      180
      collision_free : VMM      216 + 向量    108 =      324
      VMM总计: 1,296 -> 907 (DCIM优化)
      向量总计: 612
      因子图总计: 1,519
robot       _control     : VMM写入    1,519
    因子图VMM/向量操作写入统计 (localization):
      prior          : VMM        0 + 向量      0 =        0
      imu            : VMM        0 + 向量      0 =        0
      gps            : VMM        0 + 向量      0 =        0
      camera_lidar   : VMM        0 + 向量      0 =        0
      VMM总计: 0 -> 0 (DCIM优化)
      向量总计: 0
      因子图总计: 0
manipulator _localization: VMM写入        0
    因子图VMM/向量操作写入统计 (planning):
      smooth         : VMM      368 + 向量    184 =      552
      collision_free : VMM      544 + 向量    204 =      748
      kinematics_dynamics: VMM      816 + 向量    272 =    1,088
      VMM总计: 1,728 -> 1,209 (DCIM优化)
      向量总计: 660
      因子图总计: 1,869
manipulator _planning    : VMM写入    1,869
    因子图VMM/向量操作写入统计 (control):
      kinematics_dynamics: VMM      432 + 向量    288 =      720
      smooth         : VMM       48 + 向量     48 =       96
      collision_free : VMM       96 + 向量     72 =      168
      VMM总计: 576 -> 403 (DCIM优化)
      向量总计: 408
      因子图总计: 811
manipulator _control     : VMM写入      811
    因子图VMM/向量操作写入统计 (localization):
      prior          : VMM      108 + 向量     72 =      180
      imu            : VMM    1,350 + 向量    675 =    2,025
      gps            : VMM      450 + 向量    150 =      600
      camera_lidar   : VMM    3,024 + 向量  1,344 =    4,368
      VMM总计: 4,932 -> 3,452 (DCIM优化)
      向量总计: 2,241
      因子图总计: 5,693
autovehicle _localization: VMM写入    5,693
    因子图VMM/向量操作写入统计 (planning):
      smooth         : VMM      216 + 向量     72 =      288
      collision_free : VMM      360 + 向量     90 =      450
      kinematics_dynamics: VMM      540 + 向量    120 =      660
      VMM总计: 1,116 -> 781 (DCIM优化)
      向量总计: 282
      因子图总计: 1,063
autovehicle _planning    : VMM写入    1,063
    因子图VMM/向量操作写入统计 (control):
      kinematics_dynamics: VMM      525 + 向量    140 =      665
      smooth         : VMM       50 + 向量     20 =       70
      collision_free : VMM      100 + 向量     30 =      130
      VMM总计: 675 -> 472 (DCIM优化)
      向量总计: 190
      因子图总计: 662
autovehicle _control     : VMM写入      662
    因子图VMM/向量操作写入统计 (localization):
      prior          : VMM       36 + 向量     12 =       48
      imu            : VMM      432 + 向量    108 =      540
      gps            : VMM      144 + 向量     24 =      168
      camera_lidar   : VMM      972 + 向量    216 =    1,188
      VMM总计: 1,584 -> 1,108 (DCIM优化)
      向量总计: 360
      因子图总计: 1,468
quadrotor   _localization: VMM写入    1,468
    因子图VMM/向量操作写入统计 (planning):
      smooth         : VMM      864 + 向量    144 =    1,008
      collision_free : VMM    1,440 + 向量    180 =    1,620
      kinematics_dynamics: VMM    2,160 + 向量    240 =    2,400
      VMM总计: 4,464 -> 3,124 (DCIM优化)
      向量总计: 564
      因子图总计: 3,688
quadrotor   _planning    : VMM写入    3,688
    因子图VMM/向量操作写入统计 (control):
      kinematics_dynamics: VMM    3,024 + 向量    336 =    3,360
      smooth         : VMM      288 + 向量     48 =      336
      collision_free : VMM      576 + 向量     72 =      648
      VMM总计: 3,888 -> 2,721 (DCIM优化)
      向量总计: 456
      因子图总计: 3,177
quadrotor   _control     : VMM写入    3,177

🎯 完整的12算法分析结果 (基于修正的反向传播写逻辑)
================================================================================
反向传播写逻辑: 基于输出内容而非节点类型
- 输出I (单位矩阵): 跳过写入
- 输出矩阵/向量: 需要与前一步反向传播相乘，涉及写入
- 反向传播有严格的执行顺序依赖


📐 基于ORIANNA框架的完整因子图误差公式和写入过程
================================================================================
使用 ⊕ (复合/加法) 和 ⊖ (求差/减法) 算子

🔍 A. 测量因子 (Measurement Factors) - 定位算法
------------------------------------------------------------

📋 A1. 先验因子 (Prior Factor):
   公式: f(xᵢ) = xᵢ ⊖ z_prior
   写入过程:
   步骤1: xᵢ ⊖ z_prior → Log(R_prior^T * R_i) → 3次写入 (Log操作)
   步骤2: R_prior^T * R_i → 9次写入 (RR操作)
   步骤3: R_prior^T * (t_i - t_prior) → 3次写入 (RV操作)
   ✅ 先验因子: 15次写入/因子

📋 A2. IMU因子 (IMU Factor):
   公式: f(xᵢ, xⱼ) = (xⱼ ⊖ xᵢ) ⊖ z_imu
   写入过程:
   步骤1: xⱼ ⊖ xᵢ → Log(R_i^T * R_j) → 3次写入
   步骤2: R_i^T * R_j → 9次写入 (RR操作)
   步骤3: R_i^T * (t_j - t_i) → 3次写入 (RV操作)
   步骤4: (xⱼ ⊖ xᵢ) ⊖ z_imu → 再次⊖操作 → 15次写入
   ✅ IMU因子: 30次写入/因子

📋 A3. GPS因子 (GPS Factor):
   公式: f(xᵢ) = p(xᵢ) - z_gps
   写入过程:
   步骤1: p(xᵢ) - z_gps → 0次写入 (SIMD向量减法)
   ✅ GPS因子: 0次写入/因子 (纯SIMD操作)

📋 A4. 相机/雷达因子 (Camera/LiDAR Factor):
   公式: f(xᵢ, lⱼ) = π(lⱼ ⊖ xᵢ) - z_sensor
   写入过程:
   步骤1: lⱼ ⊖ xᵢ → Log(R_i^T * R_landmark) → 3次写入
   步骤2: R_i^T * R_landmark → 9次写入 (RR操作)
   步骤3: R_i^T * (t_landmark - t_i) → 3次写入 (RV操作)
   步骤4: π(局部坐标) → 投影计算 → 6次写入 (相机模型)
   步骤5: 投影结果 - z_sensor → 0次写入 (SIMD减法)
   ✅ 相机/雷达因子: 21次写入/因子

🔍 B. 约束因子 (Constraint Factors) - 规划和控制算法
------------------------------------------------------------

📋 B1. 平滑因子 (Smooth Factor):
   公式: f(xᵢ, xᵢ₊₁) = xᵢ₊₁ ⊖ xᵢ
   写入过程:
   步骤1: xᵢ₊₁ ⊖ xᵢ → Log(R_i^T * R_{i+1}) → 3次写入
   步骤2: R_i^T * R_{i+1} → 9次写入 (RR操作)
   步骤3: R_i^T * (t_{i+1} - t_i) → 3次写入 (RV操作)
   ✅ 平滑因子: 15次写入/因子

📋 B2. 无碰撞因子 (Collision-free Factor):
   公式: f(xᵢ) = max(0, ε - SDF(xᵢ ⊕ cₖ))
   写入过程:
   步骤1: xᵢ ⊕ cₖ → Log(R_i * R_k) → 3次写入
   步骤2: R_i * R_k → 9次写入 (RR操作)
   步骤3: t_i + R_i * t_k → 3次写入 (RV操作)
   步骤4: SDF查询 → 1次写入 (距离场查询)
   步骤5: max(0, ε - SDF) → 0次写入 (SIMD操作)
   ✅ 无碰撞因子: 16次写入/因子 (每个关键点)

📋 B3. 运动学/动力学因子 (Kinematics/Dynamics Factor):
   公式: f(xᵢ, uᵢ, xᵢ₊₁) = xᵢ₊₁ ⊖ G(xᵢ, uᵢ)
   写入过程:
   步骤1: G(xᵢ, uᵢ) → 状态转移函数 → variable_dim×4次写入
   步骤2: xᵢ₊₁ ⊖ G(xᵢ, uᵢ) → ⊖操作 → 15次写入
   ✅ 运动学/动力学因子: 15 + variable_dim×4次写入/因子

🔧 H矩阵反向遍历过程 (基于蓝色箭头和⊖操作)
------------------------------------------------------------
所有因子的反向传播都基于⊖操作的MO-DFG反向求导:
⊖操作: ξ₁ ⊖ ξ₂ = <Log(R₂ᵀR₁), R₂ᵀ(t₁ - t₂)>

📋 ⊖操作的反向传播 (修正的写逻辑):
   基于'输出I跳过，涉及矩阵/向量乘法且与前一步结果相乘才写入':
   1️⃣ Log → RT: 输出∂Log/∂(R₂ᵀR₁) → 梯度矩阵 → 与前一步相乘 → 3次写入
   2️⃣ RT → RR: 输出I (单位矩阵) → 跳过写入 → 0次写入
   3️⃣ RR → 分支:
      - 对R₁: 输出∂RR/∂R₁ → 梯度矩阵 → 与前一步相乘 → 9次写入
      - 对R₂: 输出∂RR/∂R₂ → 梯度矩阵 → 与前一步相乘 → 9次写入
   4️⃣ RV → VP: 输出∂RV/∂(t₁-t₂) → 梯度向量 → 与前一步相乘 → 3次写入
   5️⃣ VP → 分支:
      - 对t₁: 输出I → 跳过写入 → 0次写入
      - 对t₂: 输出-I → 跳过写入 → 0次写入
   ✅ 每个⊖操作的反向传播: 24次写入

📋 DCIM替换ACIM的优化标注:
   🔄 可用DCIM替换的操作 (不需要写RRAM):
   • RT操作: transpose → DCIM重排 → 0次RRAM写入
   • VP操作: 向量加减 → DCIM计算 → 0次RRAM写入
   • 单位矩阵I相关操作 → DCIM处理 → 0次RRAM写入
   ⚡ 需要RRAM写入的操作:
   • RR操作: 矩阵乘法 → ACIM计算 → 需要RRAM写入
   • RV操作: 矩阵向量乘法 → ACIM计算 → 需要RRAM写入
   • Log/Exp操作: 复杂函数 → ACIM计算 → 需要RRAM写入

📋 不同因子的反向传播总计:
   • 先验因子: 1×⊖操作 → 24次RRAM写入
   • IMU因子: 2×⊖操作 → 48次RRAM写入
   • GPS因子: 0×⊖操作 → 0次RRAM写入 (纯DCIM)
   • 相机/雷达因子: 1×⊖操作 + 投影反向 → 24+6=30次RRAM写入
   • 平滑因子: 1×⊖操作 → 24次RRAM写入
   • 无碰撞因子: 1×⊕操作 + SDF反向 → 24+1=25次RRAM写入
   • 运动学/动力学因子: 1×⊖操作 + G反向 → 24+variable_dim×4次RRAM写入

📋 真实规模因子图估计:
   基于SLAM系统的典型配置:
   • 位姿节点: 1000个
   • 路标节点: 5000个
   • IMU因子: 999个 (连续位姿间)
   • 相机因子: 15000个 (平均每个位姿观测15个路标)
   • 先验因子: 1个 (起始位姿)
   • GPS因子: 100个 (稀疏GPS)
   • 平滑因子: 999个 (轨迹平滑)
   • 无碰撞因子: 5000个 (每个位姿5个关键点)
   • 动力学因子: 999个 (状态转移)

🔍 2. PLANNING 算法
--------------------------------------------------
📋 机器人运动学公式 (基于⊕操作):
   前向运动学: ξ_end = ξ_base ⊕ ξ_joint₁ ⊕ ξ_joint₂ ⊕ ... ⊕ ξ_jointₙ
   展开形式: ξ_end = <Log(R_base·R_j1·...·R_jn), t_base + R_base·(t_j1 + R_j1·(t_j2 + ...))>

📋 具体的误差计算公式:
   1. 位置误差: e_pos = ξ_desired ⊖ ξ_actual
      = <Log(R_actual^T · R_desired), R_actual^T · (t_desired - t_actual)>
   2. 关节约束误差: e_joint = θ_actual - θ_desired (关节角度偏差)
   3. 路径连续性误差: e_continuity = ξᵢ₊₁ ⊖ (ξᵢ ⊕ Δξᵢ)
      其中 Δξᵢ 是期望的路径增量

📋 误差计算写入过程 (基于⊕和⊖操作):
   步骤1: R_actual^T 计算 → 0次写入 (SIMD操作)
   步骤2: R_actual^T · R_desired → 9次写入 (RR操作)
   步骤3: Log(R_actual^T · R_desired) → 3次写入 (Log操作)
   步骤4: t_desired - t_actual → 0次写入 (VP操作)
   步骤5: R_actual^T · (t_desired - t_actual) → 3次写入 (RV操作)
   步骤6: 关节约束计算 → variable_dim次写入
   ✅ 规划误差总计: 15 + variable_dim次写入/因子

📋 H矩阵反向遍历过程 (基于蓝色箭头方向):
   参考MO-DFG图中蓝色箭头从右到左的反向传播:
   1️⃣ Log → RT: 输出梯度矩阵 → 需要写入 → 3次写入
   2️⃣ RT → RR: 输出I (单位矩阵) → 跳过写入 → 0次写入
   3️⃣ RR → 输出: 梯度矩阵 → 需要写入 → 9次写入
   4️⃣ RV → VP: 输出梯度向量 → 需要写入 → 3次写入
   5️⃣ VP → 输出: I (单位矩阵) → 跳过写入 → 0次写入
   ✅ 反向传播总计: 15次写入/因子

🔍 3. CONTROL 算法
--------------------------------------------------
📋 具体的动力学模型公式:
   刚体动力学: M(ξ)ξ̈ + C(ξ,ξ̇)ξ̇ + G(ξ) = τ
   其中: M(ξ)为惯性矩阵, C(ξ,ξ̇)为科里奥利项, G(ξ)为重力项, τ为控制力矩
   状态空间形式: ξ̇ = [ξ̇₁; ξ̇₂], ξ̇₂ = M⁻¹(τ - C·ξ̇₂ - G)

📋 具体的误差计算公式:
   1. 跟踪误差: e_track = ξ_ref ⊖ ξ_actual
      = <Log(R_actual^T · R_ref), R_actual^T · (t_ref - t_actual)>
   2. 速度误差: e_vel = ξ̇_ref - ξ̇_actual (李代数空间中的差值)
   3. 动力学一致性误差: e_dyn = M·ξ̈ + C·ξ̇ + G - τ

📋 误差计算写入过程 (基于⊕和⊖操作):
   步骤1: R_actual^T 计算 → 0次写入 (SIMD操作)
   步骤2: R_actual^T · R_ref → 9次写入 (RR操作)
   步骤3: Log(R_actual^T · R_ref) → 3次写入 (Log操作)
   步骤4: t_ref - t_actual → 0次写入 (VP操作)
   步骤5: R_actual^T · (t_ref - t_actual) → 3次写入 (RV操作)
   步骤6: 动力学项计算 → variable_dim×2次写入 (M, C, G计算)
   ✅ 控制误差总计: 15 + variable_dim×2次写入/因子

📋 H矩阵反向遍历过程 (基于蓝色箭头方向):
   参考MO-DFG图中蓝色箭头从右到左的反向传播:
   1️⃣ Log → RT: 输出梯度矩阵 → 需要写入 → 3次写入
   2️⃣ RT → RR: 输出I (单位矩阵) → 跳过写入 → 0次写入
   3️⃣ RR → 输出: 梯度矩阵 → 需要写入 → 9次写入
   4️⃣ RV → VP: 输出梯度向量 → 需要写入 → 3次写入
   5️⃣ VP → 输出: I (单位矩阵) → 跳过写入 → 0次写入
   6️⃣ 动力学反向传播: 基于M, C, G的梯度 → variable_dim×2次写入
   ✅ 反向传播总计: 15 + variable_dim×2次写入/因子

================================================================================

📊 ROBOT APPLICATION
--------------------------------------------------
    QR分解详细写入统计:
      预处理阶段: 3
      分解阶段: 6
      旋转应用阶段: 16 -> 9 (DCIM优化)
      回代求解阶段: 3
      QR总计: 21
   localization:  505,786 次写入 (误差:206,991 + H矩阵:298,785 + QR:10)
    QR分解详细写入统计:
      预处理阶段: 15
      分解阶段: 30
      旋转应用阶段: 140 -> 84 (DCIM优化)
      回代求解阶段: 6
      QR总计: 135
   planning    :  231,678 次写入 (误差:93,762 + H矩阵:137,849 + QR:67)
    QR分解详细写入统计:
      预处理阶段: 3
      分解阶段: 6
      旋转应用阶段: 16 -> 9 (DCIM优化)
      回代求解阶段: 3
      QR总计: 21
   control     :  147,128 次写入 (误差:59,166 + H矩阵:87,952 + QR:10)
   总计          :  884,592 次写入

📊 MANIPULATOR APPLICATION
--------------------------------------------------
    QR分解详细写入统计:
      预处理阶段: 3
      分解阶段: 6
      旋转应用阶段: 16 -> 9 (DCIM优化)
      回代求解阶段: 3
      QR总计: 21
   localization:  404,626 次写入 (误差:165,591 + H矩阵:239,025 + QR:10)
    QR分解详细写入统计:
      预处理阶段: 21
      分解阶段: 42
      旋转应用阶段: 224 -> 134 (DCIM优化)
      回代求解阶段: 7
      QR总计: 204
   planning    :  304,647 次写入 (误差:122,039 + H矩阵:182,506 + QR:102)
    QR分解详细写入统计:
      预处理阶段: 6
      分解阶段: 12
      旋转应用阶段: 40 -> 24 (DCIM优化)
      回代求解阶段: 4
      QR总计: 46
   control     :  168,734 次写入 (误差:67,803 + H矩阵:100,908 + QR:23)
   总计          :  878,007 次写入

📊 AUTOVEHICLE APPLICATION
--------------------------------------------------
    QR分解详细写入统计:
      预处理阶段: 3
      分解阶段: 6
      旋转应用阶段: 16 -> 9 (DCIM优化)
      回代求解阶段: 3
      QR总计: 21
   localization:  606,946 次写入 (误差:248,391 + H矩阵:358,545 + QR:10)
    QR分解详细写入统计:
      预处理阶段: 6
      分解阶段: 12
      旋转应用阶段: 40 -> 24 (DCIM优化)
      回代求解阶段: 4
      QR总计: 46
   planning    :  178,445 次写入 (误差:72,207 + H矩阵:106,215 + QR:23)
    QR分解详细写入统计:
      预处理阶段: 3
      分解阶段: 6
      旋转应用阶段: 16 -> 9 (DCIM优化)
      回代求解阶段: 3
      QR总计: 21
   control     :  179,848 次写入 (误差:72,286 + H矩阵:107,552 + QR:10)
   总计          :  965,239 次写入

📊 QUADROTOR APPLICATION
--------------------------------------------------
    QR分解详细写入统计:
      预处理阶段: 15
      分解阶段: 30
      旋转应用阶段: 140 -> 84 (DCIM优化)
      回代求解阶段: 6
      QR总计: 135
   localization:  101,203 次写入 (误差:41,391 + H矩阵:59,745 + QR:67)
    QR分解详细写入统计:
      预处理阶段: 15
      分解阶段: 30
      旋转应用阶段: 140 -> 84 (DCIM优化)
      回代求解阶段: 6
      QR总计: 135
   planning    :   46,318 次写入 (误差:18,722 + H矩阵:27,529 + QR:67)
    QR分解详细写入统计:
      预处理阶段: 6
      分解阶段: 12
      旋转应用阶段: 40 -> 24 (DCIM优化)
      回代求解阶段: 4
      QR总计: 46
   control     :   43,774 次写入 (误差:17,563 + H矩阵:26,188 + QR:23)
   总计          :  191,295 次写入

🎯 12算法总计: 2,919,133 次RRAM写入
平均每算法: 243,261 次写入

🎯 RRAM CIM应用评估统计:
============================================================
总RRAM写入: 2,919,133
VMM/向量操作写入: 28,023
VMM占比: 1.0%
平均每算法: 243,261
平均VMM每算法: 2,335
适合RRAM CIM应用: ❌ 否
❌ 限制因素: VMM操作占比过低

关键技术特性:
1. 分步骤QR分解: Preprocessing → Factorization → 旋转应用 → 回代求解
2. DCIM dynamicVMM: 专门处理动态权重，减少静态存储写入
3. 因子图误差函数: 基于⊕/⊖算子的7种因子类型
4. MO-DFG反向传播: 基于输出内容的写入逻辑优化
