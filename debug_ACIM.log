
Begin BookSim Configuration File: ./plugins/booksim2/runfiles/mesh_express.cfg
topology = mesh;
k = 7;
n = 2;
router = iq;
routing_function = dor;
num_vcs = 4;
channel_width = 128;
vc_buf_size = 1024;
wait_for_tail_credit = 0;
vc_allocator = islip;
sw_allocator = islip;
alloc_iters = 1;
network_link_latency = 1;
credit_delay = 2;
routing_delay = 0;
vc_alloc_delay = 1;
sw_alloc_delay = 1;
input_speedup = 1;
output_speedup = 1;
internal_speedup = 1.0;
priority = schedule;
//traffic = transpose;
//packet_size = 20;
sim_type = scale;
//injection_rate = 0.005;
watch_out = /root/Projects/Event-Driven-Simulator/result/booksim.log;
//viewer_trace = 1;
watch_flits = {111};
//watch_packets = {1142};
//watch_all_packets = 1;
watch_router_buffers = {1};
watch_inr_flows = {7};
print_activity = 1;
vnets = 2;
request_begin_vc = 0;
request_end_vc = 3;
//reply_begin_vc = 4;
//reply_end_vc = 7;
frequency = 200;
sim_power = 1;
behavior_tables_path = mapping_scheduling/;
mode_noc_or_nop = noc;

End BookSim Configuration File: ./plugins/booksim2/runfiles/mesh_express.cfg
input_args:  Namespace(config='configs/config.yaml', exp_tag='', module_layout='configs/module_layout.txt')
current_time:  2025-07-04_12-15-42
scheduling tag:  auto_scheduling
Total 15 operations required for QR decomposition with 6 rows and 12 columns.
total unfolded Operators:
Store: 13
Expmapping: 2
Transpose: 4
WriteWeights: 22
StaticVMM: 27
Subtract: 3
Logmapping: 1
Preprocessing: 15
Factorization: 15
Backsubstitution: 1

op name:Skew_x_Rj_0
op type:StaticVMM
tensor shape:(3, 3)
dependent op name:Skew_ti_minus_tj_0
dependent op type:StaticVMM
dependent_object tensor shape:(9, 3)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S0_C0_R01_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:Augmented_Matrix_0
dependent op type:Transpose
dependent_object tensor shape:(13, 6)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S0_C0_R01_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:Augmented_Matrix_0
dependent op type:Transpose
dependent_object tensor shape:(13, 6)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S0_C0_R01_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:Augmented_Matrix_0
dependent op type:Transpose
dependent_object tensor shape:(13, 6)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S0_C2_R23_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:Augmented_Matrix_0
dependent op type:Transpose
dependent_object tensor shape:(13, 6)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S0_C2_R23_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:Augmented_Matrix_0
dependent op type:Transpose
dependent_object tensor shape:(13, 6)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S0_C2_R23_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:Augmented_Matrix_0
dependent op type:Transpose
dependent_object tensor shape:(13, 6)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S0_C4_R45_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:Augmented_Matrix_0
dependent op type:Transpose
dependent_object tensor shape:(13, 6)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S0_C4_R45_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:Augmented_Matrix_0
dependent op type:Transpose
dependent_object tensor shape:(13, 6)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S0_C4_R45_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:Augmented_Matrix_0
dependent op type:Transpose
dependent_object tensor shape:(13, 6)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S1_C0_R02_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S0_C0_R01_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S1_C0_R02_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S0_C2_R23_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S1_C0_R02_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S0_C0_R01_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S1_C0_R02_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S0_C2_R23_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S1_C0_R02_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S0_C0_R01_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S1_C0_R02_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S0_C2_R23_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S1_C1_R13_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S0_C0_R01_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S1_C1_R13_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S0_C2_R23_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S1_C1_R13_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S0_C0_R01_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S1_C1_R13_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S0_C2_R23_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S1_C1_R13_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S0_C0_R01_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S1_C1_R13_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S0_C2_R23_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S2_C0_R03_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S1_C1_R13_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S2_C0_R03_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S1_C0_R02_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S2_C0_R03_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S1_C1_R13_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S2_C0_R03_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S1_C0_R02_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S2_C0_R03_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S1_C1_R13_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S2_C0_R03_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S1_C0_R02_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S2_C1_R12_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S1_C1_R13_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S2_C1_R12_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S1_C0_R02_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S2_C1_R12_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S1_C1_R13_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S2_C1_R12_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S1_C0_R02_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S2_C1_R12_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S1_C1_R13_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S2_C1_R12_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S1_C0_R02_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S3_C0_R04_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S0_C4_R45_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 9)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S3_C0_R04_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S2_C0_R03_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S3_C0_R04_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S0_C4_R45_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 9)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S3_C0_R04_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S2_C0_R03_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S3_C0_R04_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S0_C4_R45_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 9)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S3_C0_R04_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S2_C0_R03_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S3_C1_R15_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S0_C4_R45_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 9)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S3_C1_R15_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S2_C1_R12_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S3_C1_R15_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S0_C4_R45_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 9)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S3_C1_R15_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S2_C1_R12_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S3_C1_R15_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S0_C4_R45_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 9)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S3_C1_R15_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S2_C1_R12_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S4_C0_R05_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S3_C1_R15_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S4_C0_R05_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S3_C0_R04_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S4_C0_R05_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S3_C1_R15_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S4_C0_R05_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S3_C0_R04_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S4_C0_R05_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S3_C1_R15_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S4_C0_R05_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S3_C0_R04_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S4_C1_R14_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S3_C1_R15_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S4_C1_R14_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S3_C0_R04_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S4_C1_R14_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S3_C1_R15_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S4_C1_R14_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S3_C0_R04_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S4_C1_R14_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S3_C1_R15_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S4_C1_R14_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S3_C0_R04_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S5_C2_R24_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S4_C1_R14_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S5_C2_R24_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S2_C1_R12_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S5_C2_R24_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S4_C1_R14_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S5_C2_R24_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S2_C1_R12_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S5_C2_R24_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S4_C1_R14_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S5_C2_R24_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S2_C1_R12_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 12)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S5_C3_R35_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S4_C0_R05_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S5_C3_R35_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S2_C0_R03_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S5_C3_R35_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S4_C0_R05_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S5_C3_R35_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S2_C0_R03_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S5_C3_R35_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S4_C0_R05_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S5_C3_R35_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S2_C0_R03_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 13)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S6_C2_R25_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S5_C3_R35_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 10)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S6_C2_R25_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S5_C2_R24_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S6_C2_R25_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S5_C3_R35_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 10)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S6_C2_R25_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S5_C2_R24_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S6_C2_R25_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S5_C3_R35_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 10)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S6_C2_R25_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S5_C2_R24_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S6_C3_R34_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S5_C3_R35_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 10)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Preprocessing_S6_C3_R34_0
op type:Preprocessing
tensor shape:(2, 1)
dependent op name:QR_Rotate_S5_C2_R24_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S6_C3_R34_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S5_C3_R35_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 10)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Givens_S6_C3_R34_0
op type:Factorization
tensor shape:(3, 1)
dependent op name:QR_Rotate_S5_C2_R24_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S6_C3_R34_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S5_C3_R35_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 10)
WARNING: Sub-matrix dimensions out of bounds
op name:QR_Rotate_S6_C3_R34_0
op type:StaticVMM
tensor shape:(2, 2)
dependent op name:QR_Rotate_S5_C2_R24_0
dependent op type:StaticVMM
dependent_object tensor shape:(2, 11)
WARNING: Sub-matrix dimensions out of bounds
ops: 103
min resources needed (total) for RRAM-based modules: 27
current acim_l module counts: 28
Starting x_y_allocation with 103 operations
allocated (1,1) for Store [1/103]
allocated (1,5) for Store [2/103]
allocated (2,2) for Store [3/103]
allocated (2,4) for Store [4/103]
allocated (3,3) for Store [5/103]
allocated (4,2) for Store [6/103]
allocated (4,4) for Store [7/103]
allocated (5,1) for Store [8/103]
allocated (0,3) for Expmapping [9/103]
allocated (1,3) for Expmapping [10/103]
allocated (2,3) for Transpose [11/103]
allocated (3,0) for Transpose [12/103]
allocated (3,1) for Transpose [13/103]
allocated (0,0) for WriteWeights [14/103]
allocated (0,1) for WriteWeights [15/103]
allocated (0,2) for WriteWeights [16/103]
allocated (0,4) for WriteWeights [17/103]
allocated (0,5) for StaticVMM [18/103]
allocated (0,6) for StaticVMM [19/103]
allocated (3,2) for Subtract [20/103]
allocated (1,0) for StaticVMM [21/103]
allocated (3,4) for Subtract [22/103]
allocated (1,2) for StaticVMM [23/103]
allocated (3,5) for Logmapping [24/103]
allocated (5,5) for Store [25/103]
allocated (1,1) for Store [26/103]
allocated (1,4) for StaticVMM [27/103]
allocated (1,6) for StaticVMM [28/103]
allocated (2,0) for StaticVMM [29/103]
allocated (2,1) for WriteWeights [30/103]
allocated (2,5) for StaticVMM [31/103]
allocated (2,6) for WriteWeights [32/103]
allocated (4,0) for StaticVMM [33/103]
allocated (1,5) for Store [34/103]
allocated (4,1) for StaticVMM [35/103]
allocated (2,2) for Store [36/103]
allocated (3,6) for Subtract [37/103]
allocated (4,5) for WriteWeights [38/103]
allocated (4,6) for StaticVMM [39/103]
allocated (5,0) for StaticVMM [40/103]
allocated (4,3) for Transpose [41/103]
allocated (5,3) for Preprocessing [42/103]
allocated (6,3) for Factorization [43/103]
allocated (5,2) for WriteWeights [44/103]
allocated (5,4) for StaticVMM [45/103]
allocated (0,3) for Preprocessing [46/103]
allocated (1,3) for Factorization [47/103]
allocated (5,6) for WriteWeights [48/103]
allocated (6,0) for StaticVMM [49/103]
allocated (2,3) for Preprocessing [50/103]
allocated (3,0) for Factorization [51/103]
allocated (6,1) for WriteWeights [52/103]
allocated (6,2) for StaticVMM [53/103]
allocated (3,1) for Preprocessing [54/103]
allocated (3,2) for Factorization [55/103]
allocated (6,4) for WriteWeights [56/103]
allocated (6,5) for StaticVMM [57/103]
allocated (3,4) for Preprocessing [58/103]
allocated (3,5) for Factorization [59/103]
allocated (6,6) for WriteWeights [60/103]
allocated (0,0) for StaticVMM [61/103]
allocated (3,6) for Preprocessing [62/103]
allocated (4,3) for Factorization [63/103]
allocated (0,1) for WriteWeights [64/103]
allocated (0,2) for StaticVMM [65/103]
allocated (5,3) for Preprocessing [66/103]
allocated (6,3) for Factorization [67/103]
allocated (0,4) for WriteWeights [68/103]
allocated (0,5) for StaticVMM [69/103]
allocated (0,3) for Preprocessing [70/103]
allocated (1,3) for Factorization [71/103]
allocated (0,6) for WriteWeights [72/103]
allocated (1,0) for StaticVMM [73/103]
allocated (2,3) for Preprocessing [74/103]
allocated (3,0) for Factorization [75/103]
allocated (1,2) for WriteWeights [76/103]
allocated (1,4) for StaticVMM [77/103]
allocated (3,1) for Preprocessing [78/103]
allocated (3,2) for Factorization [79/103]
allocated (1,6) for WriteWeights [80/103]
allocated (2,0) for StaticVMM [81/103]
allocated (3,4) for Preprocessing [82/103]
allocated (3,5) for Factorization [83/103]
allocated (2,1) for WriteWeights [84/103]
allocated (2,5) for StaticVMM [85/103]
allocated (3,6) for Preprocessing [86/103]
allocated (4,3) for Factorization [87/103]
allocated (2,6) for WriteWeights [88/103]
allocated (4,0) for StaticVMM [89/103]
allocated (5,3) for Preprocessing [90/103]
allocated (6,3) for Factorization [91/103]
allocated (4,1) for WriteWeights [92/103]
allocated (4,5) for StaticVMM [93/103]
allocated (0,3) for Preprocessing [94/103]
allocated (1,3) for Factorization [95/103]
allocated (4,6) for WriteWeights [96/103]
allocated (5,0) for StaticVMM [97/103]
allocated (2,3) for Preprocessing [98/103]
allocated (3,0) for Factorization [99/103]
allocated (5,2) for WriteWeights [100/103]
allocated (5,4) for StaticVMM [101/103]
allocated (2,4) for Store [102/103]
allocated (3,1) for Backsubstitution [103/103]
total flows: 0
total unfolded Instructions:
Store: 51
Transfer: 235
Expmapping: 6
Transpose: 4
WriteWeights: 22
StaticVMM: 78
Subtract: 9
Logmapping: 3
Preprocessing: 30
Factorization: 45
Backsubstitution: 6

generate 489 instructions: 254 computation instructions + 235 communication instructions
------current cycle:0
have retired compute instrs:0 (expect 254)
total cycles: 1778
total computing cycles: 1723
total communication cycles: 55
finish 489 instructions: 254 computation instructions + 235 communication instructions
total unfolded Instructions:
Transfer: 235
Store: 51
Preprocessing: 30
Transpose: 4
Factorization: 45
WriteWeights: 22
Expmapping: 6
Subtract: 9
StaticVMM: 78
Logmapping: 3
Backsubstitution: 6

DroppedTransfer_expected:  [0, 0]
#retired_compute_instructions(254) and expected num_compute_instr(254) matches
--------------------------------
PPA Results:
performance:
  -Preprocessing:
    {'total_cycles': 210, 'counts': 30, 'average_cycles': 7.0, 'latency_proportion': 0.043112297269554505}
  -Store:
    {'total_cycles': 51, 'counts': 51, 'average_cycles': 1.0, 'latency_proportion': 0.01047012933689181}
  -Factorization:
    {'total_cycles': 1215, 'counts': 45, 'average_cycles': 27.0, 'latency_proportion': 0.2494354342024225}
  -Expmapping:
    {'total_cycles': 252, 'counts': 6, 'average_cycles': 42.0, 'latency_proportion': 0.051734756723465405}
  -Transpose:
    {'total_cycles': 5, 'counts': 4, 'average_cycles': 1.25, 'latency_proportion': 0.0010264832683227264}
  -WriteWeights:
    {'total_cycles': 132, 'counts': 22, 'average_cycles': 6.0, 'latency_proportion': 0.027099158283719976}
  -Subtract:
    {'total_cycles': 18, 'counts': 9, 'average_cycles': 2.0, 'latency_proportion': 0.003695339765961815}
  -StaticVMM:
    {'total_cycles': 1872, 'counts': 78, 'average_cycles': 24.0, 'latency_proportion': 0.38431533566002873}
  -Logmapping:
    {'total_cycles': 60, 'counts': 3, 'average_cycles': 20.0, 'latency_proportion': 0.012317799219872716}
  -Backsubstitution:
    {'total_cycles': 1056, 'counts': 6, 'average_cycles': 176.0, 'latency_proportion': 0.2167932662697598}
  -total: 1778 TODO
energy:
  -node:
    {'dynamic': 1.1618719883800015e-08, 'static': 1.3984323822000001e-09, 'total': 1.3017152266000015e-08}
  -dispatch_node:
    {'dynamic': 0, 'static': 0}
  -network:
    {'dynamic': 3.562559500088483e-07, 'static': 6.5594761523894915e-06, 'total': 6.91573210239834e-06}
  -dynamic: 3.678746698926483e-07
  -static: 6.560874584771691e-06
  -total: 6.928749254664339e-06
power:
  -node:
    {'dynamic': 0.0013069426191001142, 'static': 0.00015730398, 'total': 0.0014642465991001143}
  -dispatch_node:
    {'dynamic': 0.0, 'static': 0, 'total': 0.0}
  -network:
    {'router': {'dynamic_without_INR': 0.02716322922352426, 'static_without_INR': 0.7348239507913129, 'dynamic': 0.02716322922352426, 'static': 0.7348239507913129, 'total': 0.7619871800148371}, 'link': {'dynamic': 0.012910555929327074, 'static': 0.0030248852480000074, 'total': 0.01593544117732708, 'flits': 8442}, 'dynamic': 0.04007378515285133, 'static': 0.737848836039313, 'total': 0.7779226211921643}
  -dynamic: 0.041380727771951446
  -static: 0.738006140019313
  -total: 0.7793868677912644
area:
  -network:
    {'router': 8.42552381439999e-07, 'link': 4.238868479999998e-09, 'total': 8.46791249919999e-07}
  -node:
    {'total': 5.981759379999998e-05, 'ACIM_Little': 5.496820000000002e-07, 'NU': 5.917620000000001e-05, 'MU': 9.17118e-08}
  -dispatch_node:
    {'total': 0}
  -total: 6.066438504991998e-05
--------------------------------
total simulation time: 4.842803239822388s
Class 0:
Packet latency average = 44.0894
	minimum = 18
	maximum = 163
Network latency average = 37.4043
	minimum = 18
	maximum = 120
Slowest packet = 24
Flit latency average = 28.2331
	minimum = 10
	maximum = 112
Slowest flit = 412
Fragmentation average = 2.24255
	minimum = 0
	maximum = 43
Injected packet rate average = 0.00484926
	minimum = 0 (at node 42)
	maximum = 0.0121335 (at node 10)
Accepted packet rate average = 0.00484926
	minimum = 0 (at node 8)
	maximum = 0.0283114 (at node 34)
Injected flit rate average = 0.0436433
	minimum = 0 (at node 42)
	maximum = 0.109201 (at node 10)
Accepted flit rate average= 0.0436433
	minimum = 0 (at node 8)
	maximum = 0.254803 (at node 34)
Injected packet length average = 9
Accepted packet length average = 9
Total in-flight flits = 0 (0 measured)
Buffer busy stall rate = 0.00274447
Buffer conflict stall rate = 0.000288892
Buffer full stall rate = 0
Buffer reserved stall rate = 0
Crossbar conflict stall rate = 0.0423021
====== DSENT Power Summary ======
----- Complete time -----
   Cycles: 989
----- Router -------
Buffer:
   Dynamic power: 0.0160157
   Leakage power: 0.669236
Crossbar:
   Dyanmic power: 0.00290861
   Leakage power: 0.0493316
Switch allocator:
   Dyanmic power: 0
   Leakage power: 0.0157896
Clock:
   Dynamic power: 0.00823894
   Leakage power: 0.00046673
Router Total: 0.761987
   Dynamic power: 0.0271632
   Leakage power: 0.734824
   Area: 8.42552e-07
----- Link ------
Link Total: 0.0159354
   Dynamic power: 0.0129106
   Leakage power: 0.00302489
   Area: 4.23887e-09
----- Network ------
Network Total: 0.777923
   Dynamic power: 0.0400738
   Leakage power: 0.737849
Area Total: 8.46791e-07
==============================
