from typing import Dict, List, Any, Optional, Tuple, Union
import time
import logging

from .core.factor_graph_ir import FactorGraphIR
from .core.mo_dfg import MatrixOperationDFG
from .stages.code_parser import CodeParser
from .stages.mo_dfg_generator import MODFGGenerator
from .stages.forward_traversal import ForwardTraversal
from .stages.backward_propagation import BackwardPropagation
from .stages.inference_generator import InferenceGenerator
from mapping_scheduling.op_list import Operator
from utils.instruction_queue import Instruction


class CompilationResult:
    def __init__(self):
        self.factor_graph: Optional[FactorGraphIR] = None
        self.mo_dfgs: Dict[str, MatrixOperationDFG] = {}
        self.forward_instructions: List[Tuple[Operator, tuple]] = []
        self.backward_instructions: List[Tuple[Operator, tuple]] = []
        self.inference_instructions: List[Tuple[Operator, tuple]] = []
        self.compilation_time: float = 0.0
        self.statistics: Dict[str, Any] = {}
        self.success: bool = False
        self.error_message: str = ""


class OriannaCompiler:
    def __init__(self, enable_logging: bool = True):
        self.enable_logging = enable_logging
        self.logger = self._setup_logger() if enable_logging else None
        
        # Initialize compilation stages
        self.code_parser = CodeParser()
        self.mo_dfg_generator = MODFGGenerator()
        self.forward_traversal = ForwardTraversal()
        self.backward_propagation = BackwardPropagation()
        self.inference_generator = InferenceGenerator()
        
        # State
        self.last_compilation_result: Optional[CompilationResult] = None
        
    def _setup_logger(self) -> logging.Logger:
        logger = logging.getLogger('orianna_compiler')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def compile(self, 
                source: Union[str, Dict, callable],         # Factor graph specification (function, dict, or string)
                optimization_params: Optional[Dict[str, Any]] = None,       # Parameters for optimization algorithm
                target_platform: str = "event_driven_simulator") -> CompilationResult:      # Target execution platform
        result = CompilationResult()
        start_time = time.time()
        
        try:
            if self.logger:
                self.logger.info("Starting compilation")
            
            # Stage 1: Code Parsing
            if self.logger:
                self.logger.info("Stage 1: Parsing factor graph code")
            result.factor_graph = self._stage1_parse_code(source)
            
            # Stage 2: MO-DFG Generation
            if self.logger:
                self.logger.info("Stage 2: Generating matrix operation data flow graphs")
            result.mo_dfgs = self._stage2_generate_mo_dfgs(result.factor_graph)
            
            # Stage 3: Forward Traversal
            if self.logger:
                self.logger.info("Stage 3: Generating forward traversal instructions")
            result.forward_instructions = self._stage3_forward_traversal(
                result.factor_graph, result.mo_dfgs
            )
            
            # Stage 4: Backward Propagation
            if self.logger:
                self.logger.info("Stage 4: Generating backward propagation instructions")
            result.backward_instructions = self._stage4_backward_propagation(
                result.factor_graph, result.mo_dfgs
            )
            
            # Stage 5: Inference Generation
            if self.logger:
                self.logger.info("Stage 5: Generating inference instructions")
            result.inference_instructions = self._stage5_inference_generation(
                result.factor_graph, result.mo_dfgs,
                result.forward_instructions, result.backward_instructions,
                optimization_params
            )
            
            # Finalize compilation
            result.compilation_time = time.time() - start_time
            result.statistics = self._collect_compilation_statistics(result)
            result.success = True
            
            if self.logger:
                self.logger.info(f"Compilation completed successfully in {result.compilation_time:.3f}s")
                self.logger.info(f"Generated {len(result.inference_instructions)} total instructions")
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            result.compilation_time = time.time() - start_time
            
            if self.logger:
                self.logger.error(f"Compilation failed: {e}")
        
        self.last_compilation_result = result
        return result
    
    def _stage1_parse_code(self, source: Union[str, Dict, callable]) -> FactorGraphIR:
        return self.code_parser.parse(source)
    
    def _stage2_generate_mo_dfgs(self, factor_graph: FactorGraphIR) -> Dict[str, MatrixOperationDFG]:
        return self.mo_dfg_generator.generate(factor_graph)
    
    def _stage3_forward_traversal(self, factor_graph: FactorGraphIR, 
                                 mo_dfgs: Dict[str, MatrixOperationDFG]) -> List[Tuple[Operator, tuple]]:
        return self.forward_traversal.generate_rhs_instructions(factor_graph, mo_dfgs)
    
    def _stage4_backward_propagation(self, factor_graph: FactorGraphIR,
                                   mo_dfgs: Dict[str, MatrixOperationDFG]) -> List[Tuple[Operator, tuple]]:
        return self.backward_propagation.generate_coefficient_matrix_instructions(factor_graph, mo_dfgs)
    
    def _stage5_inference_generation(self, factor_graph: FactorGraphIR,
                                   mo_dfgs: Dict[str, MatrixOperationDFG],
                                   forward_instructions: List[Tuple[Operator, tuple]],
                                   backward_instructions: List[Tuple[Operator, tuple]],
                                   optimization_params: Optional[Dict[str, Any]]) -> List[Tuple[Operator, tuple]]:
        return self.inference_generator.generate_inference_instructions(
            factor_graph, mo_dfgs, forward_instructions, backward_instructions, optimization_params
        )
    
    def _collect_compilation_statistics(self, result: CompilationResult) -> Dict[str, Any]:
        stats = {
            'compilation_time': result.compilation_time,
            'factor_graph_stats': result.factor_graph.get_statistics(),
            'mo_dfg_stats': {
                factor_id: mo_dfg.get_statistics() 
                for factor_id, mo_dfg in result.mo_dfgs.items()
            },
            'forward_stats': self.forward_traversal.get_instruction_statistics(),
            'backward_stats': self.backward_propagation.get_instruction_statistics(),
            'inference_stats': self.inference_generator.get_instruction_statistics(),
            'total_instructions': len(result.inference_instructions)
        }
        
        return stats
    
    def compile_to_eds_format(self, source: Union[str, Dict, callable],
                             optimization_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        result = self.compile(source, optimization_params, "event_driven_simulator")
        if not result.success:
            raise RuntimeError(f"Compilation failed: {result.error_message}")
        
        # Convert to EDS format
        eds_config = {
            'scheduling_config': 'slam_factorgraph_orianna',
            'scheduling_setting': {
                'module_for_StaticVMM': 'acim_l',
                'module_for_DynamicVMM': 'dcim',
                'check_rram_reuse': False
            },
            'input_row_parallel_dict': self._generate_parallelism_config(result),
            'model_param': self._generate_model_params(result),
            'instruction_list': result.inference_instructions,
            'statistics': result.statistics
        }
        
        return eds_config
    
    def _generate_parallelism_config(self, result: CompilationResult) -> Dict[str, int]:
        # Default parallelism based on factor graph structure
        parallelism_config = {}
        
        for factor_id in result.factor_graph.factors.keys():
            parallelism_config[f'{factor_id}_input'] = 1
        
        return parallelism_config
    
    def _generate_model_params(self, result: CompilationResult) -> Dict[str, Any]:
        stats = result.factor_graph.get_statistics()
        
        return {
            'num_variables': stats['num_variables'],
            'num_factors': stats['num_factors'],
            'total_variable_dimension': stats['total_variable_dimension'],
            'total_measurement_dimension': stats['total_measurement_dimension'],
            'matrix_dim': 3,  # Default for 2D SLAM
            'translation_dim': 3,
            'rotation_dim': 3,
            'M_dim': stats['total_variable_dimension']
        }
    
    def create_example_slam_compilation(self) -> CompilationResult:
        # Use the example SLAM graph from code parser
        example_graph = self.code_parser.create_example_slam_graph()
        
        # Compile with default parameters
        optimization_params = {
            'max_iterations': 5,
            'tolerance': 1e-6,
            'damping_factor': 1e-3,
            'use_levenberg_marquardt': True
        }
        
        return self.compile(example_graph, optimization_params)
    
    def get_compilation_report(self) -> str:
        if not self.last_compilation_result:
            return "No compilation results available"
        
        result = self.last_compilation_result
        
        if not result.success:
            return f"Compilation failed: {result.error_message}"
        
        report = f"""
Orianna Compiler Report
=======================

Compilation Status: SUCCESS
Compilation Time: {result.compilation_time:.3f} seconds

Factor Graph Statistics:
- Variables: {result.statistics['factor_graph_stats']['num_variables']}
- Factors: {result.statistics['factor_graph_stats']['num_factors']}
- Total Variable Dimension: {result.statistics['factor_graph_stats']['total_variable_dimension']}
- Total Measurement Dimension: {result.statistics['factor_graph_stats']['total_measurement_dimension']}

Instruction Statistics:
- Forward Instructions: {len(result.forward_instructions)}
- Backward Instructions: {len(result.backward_instructions)}
- Total Inference Instructions: {len(result.inference_instructions)}

Operation Breakdown:
"""
        
        # Add operation breakdown
        for stage_name, stage_stats in [
            ('Forward', result.statistics['forward_stats']),
            ('Backward', result.statistics['backward_stats']),
            ('Inference', result.statistics['inference_stats'])
        ]:
            report += f"\n{stage_name} Stage:\n"
            for op_type, count in stage_stats.get('operation_counts', {}).items():
                report += f"  {op_type}: {count}\n"
        
        return report
