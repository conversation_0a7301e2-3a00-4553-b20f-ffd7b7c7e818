"""
Backward Propagation Engine - Stage 4 of Orianna Compiler

This module implements the fourth stage of the compilation pipeline:
using the chain rule of differentiation to traverse MO-DFGs in reverse
and generate instruction sequences for building the coefficient matrix A.
"""

from typing import Dict, List, Any, Optional, <PERSON>ple
import numpy as np

from ..core.factor_graph_ir import FactorGraphIR
from ..core.mo_dfg import MatrixOperationDFG, MODFGNode, MOOperationType
from mapping_scheduling.op_list import Operator
from utils.instruction_queue import Instruction


class BackwardPropagation:
    """
    Backward Propagation Engine
    
    Traverses MO-DFGs in reverse direction using chain rule to generate
    instructions for computing Jacobian matrices and building coefficient matrix A.
    """
    
    def __init__(self):
        self.factor_graph: Optional[FactorGraphIR] = None
        self.mo_dfgs: Dict[str, MatrixOperationDFG] = {}
        self.generated_instructions: List[Tuple[Operator, tuple]] = []
        self.variable_ordering: List[str] = []
        self.instruction_counter = 0
        
    def generate_coefficient_matrix_instructions(self, factor_graph: FactorGraphIR, 
                                               mo_dfgs: Dict[str, MatrixOperationDFG]) -> List[Tuple[Operator, tuple]]:
        """
        Generate instruction sequence for building coefficient matrix A
        
        Args:
            factor_graph: Factor graph IR
            mo_dfgs: Dictionary of MO-DFGs for each factor
            
        Returns:
            List of (Operator, mapping_info) tuples for execution
        """
        self.factor_graph = factor_graph
        self.mo_dfgs = mo_dfgs
        self.variable_ordering = factor_graph.get_variable_ordering()
        self.generated_instructions = []
        self.instruction_counter = 0
        
        # Process each factor's MO-DFG in reverse order
        for factor_id, mo_dfg in mo_dfgs.items():
            self._process_factor_backward(factor_id, mo_dfg)
        
        # Generate coefficient matrix assembly instructions
        self._generate_coefficient_matrix_assembly_instructions()
        
        return self.generated_instructions
    
    def _process_factor_backward(self, factor_id: str, mo_dfg: MatrixOperationDFG):
        """Process a single factor's MO-DFG in backward direction"""
        if not mo_dfg.is_compiled:
            mo_dfg.compile_execution_order()
        
        # Process Jacobian computation nodes
        jacobian_nodes = [node for node in mo_dfg.nodes.values() 
                         if node.operation_type == MOOperationType.JACOBIAN_COMPUTE]
        
        for jacobian_node in jacobian_nodes:
            self._generate_jacobian_instructions(factor_id, jacobian_node)
        
        # Generate information matrix weighting
        self._generate_information_matrix_instructions(factor_id, mo_dfg)
    
    def _generate_jacobian_instructions(self, factor_id: str, node: MODFGNode):
        """Generate instructions for Jacobian computation"""
        jacobian_wrt = node.operation_params.get('jacobian_wrt', 'unknown')
        error_type = node.operation_params.get('error_type', 'generic')
        
        if error_type == 'relative_pose_2d':
            self._generate_relative_pose_2d_jacobian(factor_id, node, jacobian_wrt)
        elif error_type == 'bearing_range':
            self._generate_bearing_range_jacobian(factor_id, node, jacobian_wrt)
        elif node.operation_params.get('jacobian_type') == 'identity':
            self._generate_identity_jacobian(factor_id, node)
        else:
            self._generate_generic_jacobian(factor_id, node)
    
    def _generate_relative_pose_2d_jacobian(self, factor_id: str, node: MODFGNode, jacobian_wrt: str):
        """Generate Jacobian for 2D relative pose factor"""
        if jacobian_wrt.endswith('0'):  # First pose
            # Jacobian w.r.t. first pose: -[R^T, -R^T * t_skew]
            
            # Compute rotation transpose
            rot_transpose_op = Operator(
                type='Transpose',
                data_size=(2, 2),  # 2x2 rotation matrix
                operation_size=(2, 2),  # 2x2 transposed matrix
                name=f'{factor_id}_{node.node_id}_rot_transpose',
                concurrent_tag=f'{factor_id}_jacobian'
            )
            self.generated_instructions.append((rot_transpose_op, (0, 0)))
            
            # Compute skew-symmetric matrix for translation
            skew_op = Operator(
                type='DynamicVMM',  # Use VMM for skew computation
                data_size=(2, 1),   # translation vector
                operation_size=(2, 2),  # skew matrix
                name=f'{factor_id}_{node.node_id}_skew',
                concurrent_tag=f'{factor_id}_jacobian'
            )
            self.generated_instructions.append((skew_op, (0, 0)))
            
            # Compute final Jacobian block
            jacobian_op = Operator(
                type='DynamicVMM',
                data_size=(node.output_shape.rows, node.output_shape.cols),
                operation_size=(node.output_shape.rows, node.output_shape.cols),
                name=f'{factor_id}_{node.node_id}',
                concurrent_tag=f'{factor_id}_jacobian'
            )
            self.generated_instructions.append((jacobian_op, (0, 0)))
            
            self.instruction_counter += 3
            
        else:  # Second pose
            # Jacobian w.r.t. second pose: [R^T, 0]
            jacobian_op = Operator(
                type='Transpose',
                data_size=(2, 2),
                operation_size=(node.output_shape.rows, node.output_shape.cols),
                name=f'{factor_id}_{node.node_id}',
                concurrent_tag=f'{factor_id}_jacobian'
            )
            self.generated_instructions.append((jacobian_op, (0, 0)))
            self.instruction_counter += 1
    
    def _generate_bearing_range_jacobian(self, factor_id: str, node: MODFGNode, jacobian_wrt: str):
        """Generate Jacobian for bearing-range factor"""
        if 'pose' in jacobian_wrt or jacobian_wrt.startswith('x'):
            # Jacobian w.r.t. pose
            # For bearing-range: [cos(bearing)/range, sin(bearing)/range, -sin(bearing), cos(bearing)]
            
            # Compute range and bearing
            range_bearing_op = Operator(
                type='DynamicVMM',
                data_size=(node.input_shapes[0].rows, node.input_shapes[0].cols),
                operation_size=(2, 1),  # [range, bearing]
                name=f'{factor_id}_{node.node_id}_range_bearing',
                concurrent_tag=f'{factor_id}_jacobian'
            )
            self.generated_instructions.append((range_bearing_op, (0, 0)))
            
            # Compute Jacobian elements
            jacobian_op = Operator(
                type='DynamicVMM',
                data_size=(2, 1),  # range, bearing
                operation_size=(node.output_shape.rows, node.output_shape.cols),
                name=f'{factor_id}_{node.node_id}',
                concurrent_tag=f'{factor_id}_jacobian'
            )
            self.generated_instructions.append((jacobian_op, (0, 0)))
            
            self.instruction_counter += 2
            
        else:
            # Jacobian w.r.t. landmark
            # For bearing-range: [-cos(bearing)/range, -sin(bearing)/range]
            jacobian_op = Operator(
                type='DynamicVMM',
                data_size=(node.input_shapes[0].rows, node.input_shapes[0].cols),
                operation_size=(node.output_shape.rows, node.output_shape.cols),
                name=f'{factor_id}_{node.node_id}',
                concurrent_tag=f'{factor_id}_jacobian'
            )
            self.generated_instructions.append((jacobian_op, (0, 0)))
            self.instruction_counter += 1
    
    def _generate_identity_jacobian(self, factor_id: str, node: MODFGNode):
        """Generate identity Jacobian (for prior factors)"""
        # Identity matrix can be generated or loaded
        identity_op = Operator(
            type='Load',  # Load identity matrix
            data_size=(node.output_shape.rows, node.output_shape.cols),
            operation_size=(node.output_shape.rows, node.output_shape.cols),
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_jacobian'
        )
        self.generated_instructions.append((identity_op, (0, 0)))
        self.instruction_counter += 1
    
    def _generate_generic_jacobian(self, factor_id: str, node: MODFGNode):
        """Generate generic Jacobian computation"""
        # For generic factors, use numerical differentiation or provided expressions
        jacobian_op = Operator(
            type='DynamicVMM',  # Use VMM for general Jacobian computation
            data_size=(node.input_shapes[0].rows if node.input_shapes else 1,
                      node.input_shapes[0].cols if node.input_shapes else 1),
            operation_size=(node.output_shape.rows, node.output_shape.cols),
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_jacobian'
        )
        self.generated_instructions.append((jacobian_op, (0, 0)))
        self.instruction_counter += 1
    
    def _generate_information_matrix_instructions(self, factor_id: str, mo_dfg: MatrixOperationDFG):
        """Generate instructions for information matrix weighting"""
        factor = self.factor_graph.factors[factor_id]
        measurement_dim = factor.get_measurement_dimension()
        
        # Load information matrix (inverse of noise covariance)
        load_info_op = Operator(
            type='Load',
            data_size=(measurement_dim, measurement_dim),
            operation_size=(measurement_dim, measurement_dim),
            name=f'{factor_id}_load_information_matrix',
            concurrent_tag=f'{factor_id}_info'
        )
        self.generated_instructions.append((load_info_op, (0, 0)))
        
        # Weight error vector: Omega * error
        weight_error_op = Operator(
            type='StaticVMM',  # Use StaticVMM for information matrix multiplication
            data_size=(measurement_dim, 1),     # error vector
            operation_size=(measurement_dim, 1), # weighted error
            name=f'{factor_id}_weighted_error',
            concurrent_tag=f'{factor_id}_weight'
        )
        self.generated_instructions.append((weight_error_op, (0, 0)))
        
        # Weight Jacobian matrices for each connected variable
        for var_key in factor.connected_variables:
            var_dim = self.factor_graph.variables[var_key].dimension
            
            weight_jacobian_op = Operator(
                type='StaticVMM',
                data_size=(measurement_dim, var_dim),    # Jacobian matrix
                operation_size=(measurement_dim, var_dim), # weighted Jacobian
                name=f'{factor_id}_weighted_jacobian_{var_key}',
                concurrent_tag=f'{factor_id}_weight'
            )
            self.generated_instructions.append((weight_jacobian_op, (0, 0)))
        
        self.instruction_counter += 2 + len(factor.connected_variables)
    
    def _generate_coefficient_matrix_assembly_instructions(self):
        """Generate instructions for assembling the coefficient matrix A"""
        total_var_dim = self.factor_graph.get_total_variable_dimension()
        
        # The coefficient matrix A = J^T * Omega * J is assembled from weighted Jacobians
        # This involves multiple matrix operations
        
        # Generate Jacobian transpose operations
        for factor_id, factor in self.factor_graph.factors.items():
            for var_key in factor.connected_variables:
                var_dim = self.factor_graph.variables[var_key].dimension
                measurement_dim = factor.get_measurement_dimension()
                
                # Transpose weighted Jacobian
                transpose_op = Operator(
                    type='Transpose',
                    data_size=(measurement_dim, var_dim),
                    operation_size=(var_dim, measurement_dim),
                    name=f'{factor_id}_jacobian_transpose_{var_key}',
                    concurrent_tag=f'{factor_id}_transpose'
                )
                self.generated_instructions.append((transpose_op, (0, 0)))
                
                # Compute J^T * Omega * J block
                jtoj_op = Operator(
                    type='StaticVMM',
                    data_size=(var_dim, measurement_dim),  # J^T
                    operation_size=(measurement_dim, var_dim),  # Omega * J
                    name=f'{factor_id}_jtoj_{var_key}',
                    concurrent_tag=f'{factor_id}_jtoj'
                )
                self.generated_instructions.append((jtoj_op, (0, 0)))
                
                self.instruction_counter += 2
        
        # Generate final matrix assembly
        assembly_op = Operator(
            type='Shortcut',  # Use Shortcut for matrix assembly
            data_size=(total_var_dim, total_var_dim),
            operation_size=(total_var_dim, total_var_dim),
            name='coefficient_matrix_assembly',
            concurrent_tag='matrix_assembly'
        )
        self.generated_instructions.append((assembly_op, (0, 0)))
        self.instruction_counter += 1
    
    def generate_augmented_matrix_instructions(self) -> List[Tuple[Operator, tuple]]:
        """Generate instructions for creating augmented matrix [A|b]"""
        total_var_dim = self.factor_graph.get_total_variable_dimension()
        
        # Create augmented matrix by concatenating A and b
        augmented_op = Operator(
            type='Transpose',  # Use Transpose for matrix concatenation
            data_size=(total_var_dim, total_var_dim + 1),
            operation_size=(total_var_dim, total_var_dim + 1),
            name='augmented_matrix_creation',
            concurrent_tag='augmented_matrix'
        )
        
        self.generated_instructions.append((augmented_op, (0, 0)))
        self.instruction_counter += 1
        
        return [(augmented_op, (0, 0))]
    
    def generate_linear_solver_instructions(self) -> List[Tuple[Operator, tuple]]:
        """Generate instructions for solving the linear system"""
        total_var_dim = self.factor_graph.get_total_variable_dimension()
        
        solver_instructions = []
        
        # QR decomposition
        qr_op = Operator(
            type='Factorization',  # QR factorization
            data_size=(total_var_dim, total_var_dim + 1),
            operation_size=(total_var_dim, total_var_dim + 1),
            name='qr_decomposition',
            concurrent_tag='linear_solver'
        )
        solver_instructions.append((qr_op, (0, 0)))
        
        # Back substitution
        backsubst_op = Operator(
            type='Backsubstitution',
            data_size=(total_var_dim, total_var_dim + 1),
            operation_size=(total_var_dim, 1),  # Solution vector
            name='back_substitution',
            concurrent_tag='linear_solver'
        )
        solver_instructions.append((backsubst_op, (0, 0)))
        
        self.generated_instructions.extend(solver_instructions)
        self.instruction_counter += 2
        
        return solver_instructions
    
    def get_instruction_statistics(self) -> Dict[str, Any]:
        """Get statistics about generated instructions"""
        op_counts = {}
        for op, _ in self.generated_instructions:
            op_type = op.type
            op_counts[op_type] = op_counts.get(op_type, 0) + 1
        
        return {
            'total_instructions': len(self.generated_instructions),
            'operation_counts': op_counts,
            'factors_processed': len(self.mo_dfgs)
        }
