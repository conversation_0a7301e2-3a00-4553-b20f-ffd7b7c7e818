"""
Forward Traversal Engine - Stage 3 of Orianna Compiler

This module implements the third stage of the compilation pipeline:
traversing MO-DFGs to generate instruction sequences for building
the right-hand side vector b of the linear equation system.
"""

from typing import Dict, List, Any, Optional, <PERSON>ple
import numpy as np

from ..core.factor_graph_ir import FactorGraphIR
from ..core.mo_dfg import MatrixOperationDFG, MODFGNode, MOOperationType
from mapping_scheduling.op_list import Operator
from utils.instruction_queue import Instruction


class ForwardTraversal:
    """
    Forward Traversal Engine
    
    Traverses MO-DFGs in forward direction to generate instructions
    for computing error vectors and building the RHS vector b.
    """
    
    def __init__(self):
        self.factor_graph: Optional[FactorGraphIR] = None
        self.mo_dfgs: Dict[str, MatrixOperationDFG] = {}
        self.generated_instructions: List[Tuple[Operator, tuple]] = []
        self.variable_ordering: List[str] = []
        self.instruction_counter = 0
        
    def generate_rhs_instructions(self, factor_graph: FactorGraphIR, 
                                mo_dfgs: Dict[str, MatrixOperationDFG]) -> List[Tuple[Operator, tuple]]:
        """
        Generate instruction sequence for building RHS vector b
        
        Args:
            factor_graph: Factor graph IR
            mo_dfgs: Dictionary of MO-DFGs for each factor
            
        Returns:
            List of (Operator, mapping_info) tuples for execution
        """
        self.factor_graph = factor_graph
        self.mo_dfgs = mo_dfgs
        self.variable_ordering = factor_graph.get_variable_ordering()
        self.generated_instructions = []
        self.instruction_counter = 0
        
        # Process each factor's MO-DFG
        for factor_id, mo_dfg in mo_dfgs.items():
            self._process_factor_forward(factor_id, mo_dfg)
        
        # Generate final RHS vector assembly instructions
        self._generate_rhs_assembly_instructions()
        
        return self.generated_instructions
    
    def _process_factor_forward(self, factor_id: str, mo_dfg: MatrixOperationDFG):
        """Process a single factor's MO-DFG in forward direction"""
        if not mo_dfg.is_compiled:
            mo_dfg.compile_execution_order()
        
        # Process nodes in execution order
        for node_id in mo_dfg.execution_order:
            node = mo_dfg.nodes[node_id]
            self._generate_node_instructions(factor_id, node)
    
    def _generate_node_instructions(self, factor_id: str, node: MODFGNode):
        """Generate instructions for a single MO-DFG node"""
        if node.operation_type == MOOperationType.LOAD_DATA:
            self._generate_load_instructions(factor_id, node)
        elif node.operation_type == MOOperationType.MATRIX_MULTIPLY:
            self._generate_matrix_multiply_instructions(factor_id, node)
        elif node.operation_type == MOOperationType.MATRIX_ADD:
            self._generate_matrix_add_instructions(factor_id, node)
        elif node.operation_type == MOOperationType.MATRIX_SUBTRACT:
            self._generate_matrix_subtract_instructions(factor_id, node)
        elif node.operation_type == MOOperationType.TRANSPOSE:
            self._generate_transpose_instructions(factor_id, node)
        elif node.operation_type == MOOperationType.EXPONENTIAL_MAP:
            self._generate_exponential_map_instructions(factor_id, node)
        elif node.operation_type == MOOperationType.LOGARITHM_MAP:
            self._generate_logarithm_map_instructions(factor_id, node)
        elif node.operation_type == MOOperationType.ERROR_COMPUTE:
            self._generate_error_compute_instructions(factor_id, node)
        elif node.operation_type == MOOperationType.JACOBIAN_COMPUTE:
            # Jacobian computation is handled in backward pass
            pass
        else:
            # Generic operation handling
            self._generate_generic_instructions(factor_id, node)
    
    def _generate_load_instructions(self, factor_id: str, node: MODFGNode):
        """Generate load data instructions"""
        op = Operator(
            type='Load',
            data_size=(node.output_shape.rows, node.output_shape.cols),
            operation_size=(node.output_shape.rows, node.output_shape.cols),
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_load'
        )
        
        # Mapping information (will be determined by scheduler)
        mapping_info = (0, 0)  # Default mapping, will be updated by scheduler
        
        self.generated_instructions.append((op, mapping_info))
        self.instruction_counter += 1
    
    def _generate_matrix_multiply_instructions(self, factor_id: str, node: MODFGNode):
        """Generate matrix multiplication instructions"""
        # Determine if this should be StaticVMM or DynamicVMM
        # For forward pass (error computation), typically use DynamicVMM
        op_type = 'DynamicVMM'
        
        op = Operator(
            type=op_type,
            data_size=(node.input_shapes[0].rows, node.input_shapes[0].cols),  # First matrix dimensions
            operation_size=(node.input_shapes[1].rows, node.input_shapes[1].cols),  # Second matrix dimensions
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_vmm'
        )
        
        mapping_info = (0, 0)
        self.generated_instructions.append((op, mapping_info))
        self.instruction_counter += 1
    
    def _generate_matrix_add_instructions(self, factor_id: str, node: MODFGNode):
        """Generate matrix addition instructions"""
        # Matrix addition can be implemented as element-wise operation
        op = Operator(
            type='Shortcut',  # Using Shortcut for element-wise addition
            data_size=(node.output_shape.rows, node.output_shape.cols),
            operation_size=(node.output_shape.rows, node.output_shape.cols),
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_add'
        )
        
        mapping_info = (0, 0)
        self.generated_instructions.append((op, mapping_info))
        self.instruction_counter += 1
    
    def _generate_matrix_subtract_instructions(self, factor_id: str, node: MODFGNode):
        """Generate matrix subtraction instructions"""
        op = Operator(
            type='Subtract',
            data_size=(node.output_shape.rows, node.output_shape.cols),
            operation_size=(node.output_shape.rows, node.output_shape.cols),
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_subtract'
        )
        
        mapping_info = (0, 0)
        self.generated_instructions.append((op, mapping_info))
        self.instruction_counter += 1
    
    def _generate_transpose_instructions(self, factor_id: str, node: MODFGNode):
        """Generate transpose instructions"""
        op = Operator(
            type='Transpose',
            data_size=(node.input_shapes[0].rows, node.input_shapes[0].cols),
            operation_size=(node.output_shape.rows, node.output_shape.cols),
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_transpose'
        )
        
        mapping_info = (0, 0)
        self.generated_instructions.append((op, mapping_info))
        self.instruction_counter += 1
    
    def _generate_exponential_map_instructions(self, factor_id: str, node: MODFGNode):
        """Generate exponential map instructions (SO(3) exp)"""
        op = Operator(
            type='Expmapping',
            data_size=(node.input_shapes[0].rows, 1),  # Input: rotation vector
            operation_size=(node.output_shape.rows, node.output_shape.cols),  # Output: rotation matrix
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_exp'
        )
        
        mapping_info = (0, 0)
        self.generated_instructions.append((op, mapping_info))
        self.instruction_counter += 1
    
    def _generate_logarithm_map_instructions(self, factor_id: str, node: MODFGNode):
        """Generate logarithm map instructions (SO(3) log)"""
        op = Operator(
            type='Logmapping',
            data_size=(node.input_shapes[0].rows, node.input_shapes[0].cols),  # Input: rotation matrix
            operation_size=(node.output_shape.rows, 1),  # Output: rotation vector
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_log'
        )
        
        mapping_info = (0, 0)
        self.generated_instructions.append((op, mapping_info))
        self.instruction_counter += 1
    
    def _generate_error_compute_instructions(self, factor_id: str, node: MODFGNode):
        """Generate error computation instructions"""
        # Error computation might involve multiple operations
        # For now, treat as a generic computation
        error_type = node.operation_params.get('error_type', 'generic')
        
        if error_type == 'relative_pose_2d':
            # For 2D relative pose, we need rotation and translation operations
            self._generate_relative_pose_2d_instructions(factor_id, node)
        elif error_type == 'bearing_range':
            # For bearing-range, we need trigonometric operations
            self._generate_bearing_range_instructions(factor_id, node)
        else:
            # Generic error computation
            op = Operator(
                type='DynamicVMM',  # Use VMM for general computation
                data_size=(node.input_shapes[0].rows, node.input_shapes[0].cols),
                operation_size=(node.output_shape.rows, node.output_shape.cols),
                name=f'{factor_id}_{node.node_id}',
                concurrent_tag=f'{factor_id}_error'
            )
            
            mapping_info = (0, 0)
            self.generated_instructions.append((op, mapping_info))
            self.instruction_counter += 1
    
    def _generate_relative_pose_2d_instructions(self, factor_id: str, node: MODFGNode):
        """Generate instructions for 2D relative pose computation"""
        # This involves rotation matrix computation and vector operations
        # Simplified implementation - in practice would need more detailed breakdown
        
        # Rotation computation
        rot_op = Operator(
            type='Expmapping',
            data_size=(1, 1),  # theta input
            operation_size=(2, 2),  # 2x2 rotation matrix
            name=f'{factor_id}_{node.node_id}_rotation',
            concurrent_tag=f'{factor_id}_rotation'
        )
        self.generated_instructions.append((rot_op, (0, 0)))
        
        # Translation computation
        trans_op = Operator(
            type='DynamicVMM',
            data_size=(2, 2),  # rotation matrix
            operation_size=(2, 1),  # translation vector
            name=f'{factor_id}_{node.node_id}_translation',
            concurrent_tag=f'{factor_id}_translation'
        )
        self.generated_instructions.append((trans_op, (0, 0)))
        
        self.instruction_counter += 2
    
    def _generate_bearing_range_instructions(self, factor_id: str, node: MODFGNode):
        """Generate instructions for bearing-range computation"""
        # Bearing-range involves distance and angle computation
        # Simplified implementation
        
        op = Operator(
            type='DynamicVMM',
            data_size=(node.input_shapes[0].rows, node.input_shapes[0].cols),
            operation_size=(node.output_shape.rows, node.output_shape.cols),
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_bearing_range'
        )
        
        mapping_info = (0, 0)
        self.generated_instructions.append((op, mapping_info))
        self.instruction_counter += 1
    
    def _generate_generic_instructions(self, factor_id: str, node: MODFGNode):
        """Generate instructions for generic operations"""
        op = Operator(
            type='DynamicVMM',  # Default to VMM for unknown operations
            data_size=(node.input_shapes[0].rows if node.input_shapes else 1, 
                      node.input_shapes[0].cols if node.input_shapes else 1),
            operation_size=(node.output_shape.rows, node.output_shape.cols),
            name=f'{factor_id}_{node.node_id}',
            concurrent_tag=f'{factor_id}_generic'
        )
        
        mapping_info = (0, 0)
        self.generated_instructions.append((op, mapping_info))
        self.instruction_counter += 1
    
    def _generate_rhs_assembly_instructions(self):
        """Generate instructions for assembling the final RHS vector b"""
        # The RHS vector b is assembled from all factor error vectors
        total_measurement_dim = self.factor_graph.get_total_measurement_dimension()
        
        # Generate assembly operation
        assembly_op = Operator(
            type='Shortcut',  # Use Shortcut for vector assembly
            data_size=(total_measurement_dim, 1),
            operation_size=(total_measurement_dim, 1),
            name='rhs_vector_assembly',
            concurrent_tag='rhs_assembly'
        )
        
        mapping_info = (0, 0)
        self.generated_instructions.append((assembly_op, mapping_info))
        self.instruction_counter += 1
    
    def get_instruction_statistics(self) -> Dict[str, Any]:
        """Get statistics about generated instructions"""
        op_counts = {}
        for op, _ in self.generated_instructions:
            op_type = op.type
            op_counts[op_type] = op_counts.get(op_type, 0) + 1
        
        return {
            'total_instructions': len(self.generated_instructions),
            'operation_counts': op_counts,
            'factors_processed': len(self.mo_dfgs)
        }
