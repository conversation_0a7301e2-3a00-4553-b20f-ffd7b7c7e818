"""
MO-DFG Generator - Stage 2 of Orianna Compiler

This module implements the second stage of the compilation pipeline:
analyzing mathematical expressions for each factor node and generating
corresponding Matrix Operation Data Flow Graphs (MO-DFG).
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import sympy as sp
from sympy import symbols, Matrix, diff, simplify

from ..core.factor_graph_ir import FactorGraphIR, FactorNode, FactorType
from ..core.mo_dfg import (
    MatrixOperationDFG, MODFGNode, MOOperationType, TensorShape
)


class MODFGGenerator:
    """
    Matrix Operation Data Flow Graph Generator
    
    Analyzes factor nodes and generates corresponding MO-DFGs that describe
    the matrix computations needed for error computation and Jacobian calculation.
    """
    
    def __init__(self):
        self.factor_graph: Optional[FactorGraphIR] = None
        self.mo_dfgs: Dict[str, MatrixOperationDFG] = {}
        self.variable_dimensions: Dict[str, int] = {}
        
    def generate(self, factor_graph: FactorGraphIR) -> Dict[str, MatrixOperationDFG]:
        """
        Generate MO-DFGs for all factors in the factor graph
        
        Args:
            factor_graph: Input factor graph IR
            
        Returns:
            Dict mapping factor IDs to their corresponding MO-DFGs
        """
        self.factor_graph = factor_graph
        self._extract_variable_dimensions()
        
        # Generate MO-DFG for each factor
        for factor_id, factor in factor_graph.factors.items():
            mo_dfg = self._generate_factor_mo_dfg(factor)
            self.mo_dfgs[factor_id] = mo_dfg
            
        return self.mo_dfgs
    
    def _extract_variable_dimensions(self):
        """Extract variable dimensions for use in MO-DFG generation"""
        for var_key, variable in self.factor_graph.variables.items():
            self.variable_dimensions[var_key] = variable.dimension
    
    def _generate_factor_mo_dfg(self, factor: FactorNode) -> MatrixOperationDFG:
        """Generate MO-DFG for a single factor"""
        mo_dfg = MatrixOperationDFG(factor.factor_id)
        
        # Generate nodes based on factor type
        if factor.factor_type == FactorType.PRIOR:
            self._generate_prior_factor_dfg(factor, mo_dfg)
        elif factor.factor_type == FactorType.BETWEEN:
            self._generate_between_factor_dfg(factor, mo_dfg)
        elif factor.factor_type == FactorType.UNARY:
            self._generate_unary_factor_dfg(factor, mo_dfg)
        elif factor.factor_type == FactorType.BEARING_RANGE:
            self._generate_bearing_range_factor_dfg(factor, mo_dfg)
        else:
            # Generic factor processing
            self._generate_generic_factor_dfg(factor, mo_dfg)
        
        # Compile execution order
        mo_dfg.compile_execution_order()
        
        return mo_dfg
    
    def _generate_prior_factor_dfg(self, factor: FactorNode, mo_dfg: MatrixOperationDFG):
        """Generate MO-DFG for prior factor: error = x - measurement"""
        var_key = factor.connected_variables[0]
        var_dim = self.variable_dimensions[var_key]
        measurement_dim = factor.get_measurement_dimension()
        
        # Load variable data
        load_var_node = MODFGNode(
            node_id=f"{factor.factor_id}_load_{var_key}",
            operation_type=MOOperationType.LOAD_DATA,
            input_shapes=[],
            output_shape=TensorShape(var_dim, 1),
            operation_params={'variable_key': var_key}
        )
        mo_dfg.add_node(load_var_node)
        mo_dfg.mark_input_nodes([load_var_node.node_id])
        
        # Load measurement data
        load_measurement_node = MODFGNode(
            node_id=f"{factor.factor_id}_load_measurement",
            operation_type=MOOperationType.LOAD_DATA,
            input_shapes=[],
            output_shape=TensorShape(measurement_dim, 1),
            operation_params={'data_type': 'measurement'}
        )
        mo_dfg.add_node(load_measurement_node)
        
        # Compute error: x - measurement
        error_node = MODFGNode(
            node_id=f"{factor.factor_id}_error",
            operation_type=MOOperationType.MATRIX_SUBTRACT,
            input_shapes=[TensorShape(var_dim, 1), TensorShape(measurement_dim, 1)],
            output_shape=TensorShape(var_dim, 1)
        )
        mo_dfg.add_node(error_node)
        mo_dfg.add_edge(load_var_node.node_id, error_node.node_id)
        mo_dfg.add_edge(load_measurement_node.node_id, error_node.node_id)
        
        # Jacobian computation (identity matrix for prior factor)
        jacobian_node = MODFGNode(
            node_id=f"{factor.factor_id}_jacobian_{var_key}",
            operation_type=MOOperationType.JACOBIAN_COMPUTE,
            input_shapes=[TensorShape(var_dim, 1)],
            output_shape=TensorShape(var_dim, var_dim),
            operation_params={'jacobian_type': 'identity'}
        )
        mo_dfg.add_node(jacobian_node)
        mo_dfg.add_edge(load_var_node.node_id, jacobian_node.node_id)
        
        # Mark output nodes
        mo_dfg.mark_output_nodes([error_node.node_id, jacobian_node.node_id])
    
    def _generate_between_factor_dfg(self, factor: FactorNode, mo_dfg: MatrixOperationDFG):
        """Generate MO-DFG for between factor (odometry)"""
        var1_key, var2_key = factor.connected_variables[0], factor.connected_variables[1]
        var1_dim = self.variable_dimensions[var1_key]
        var2_dim = self.variable_dimensions[var2_key]
        measurement_dim = factor.get_measurement_dimension()
        
        # Load variable data
        load_var1_node = MODFGNode(
            node_id=f"{factor.factor_id}_load_{var1_key}",
            operation_type=MOOperationType.LOAD_DATA,
            input_shapes=[],
            output_shape=TensorShape(var1_dim, 1),
            operation_params={'variable_key': var1_key}
        )
        mo_dfg.add_node(load_var1_node)
        
        load_var2_node = MODFGNode(
            node_id=f"{factor.factor_id}_load_{var2_key}",
            operation_type=MOOperationType.LOAD_DATA,
            input_shapes=[],
            output_shape=TensorShape(var2_dim, 1),
            operation_params={'variable_key': var2_key}
        )
        mo_dfg.add_node(load_var2_node)
        mo_dfg.mark_input_nodes([load_var1_node.node_id, load_var2_node.node_id])
        
        # Load measurement
        load_measurement_node = MODFGNode(
            node_id=f"{factor.factor_id}_load_measurement",
            operation_type=MOOperationType.LOAD_DATA,
            input_shapes=[],
            output_shape=TensorShape(measurement_dim, 1),
            operation_params={'data_type': 'measurement'}
        )
        mo_dfg.add_node(load_measurement_node)
        
        # For 2D poses: compute relative transformation
        if var1_dim == 3 and var2_dim == 3:  # 2D poses
            # Extract rotation components (assuming [x, y, theta] format)
            extract_theta1_node = MODFGNode(
                node_id=f"{factor.factor_id}_extract_theta1",
                operation_type=MOOperationType.LOAD_DATA,
                input_shapes=[TensorShape(var1_dim, 1)],
                output_shape=TensorShape(1, 1),
                operation_params={'extract_index': 2}  # theta is at index 2
            )
            mo_dfg.add_node(extract_theta1_node)
            mo_dfg.add_edge(load_var1_node.node_id, extract_theta1_node.node_id)
            
            # Compute rotation matrix R1
            rotation_matrix1_node = MODFGNode(
                node_id=f"{factor.factor_id}_rotation_matrix1",
                operation_type=MOOperationType.EXPONENTIAL_MAP,
                input_shapes=[TensorShape(1, 1)],
                output_shape=TensorShape(2, 2),
                operation_params={'map_type': '2d_rotation'}
            )
            mo_dfg.add_node(rotation_matrix1_node)
            mo_dfg.add_edge(extract_theta1_node.node_id, rotation_matrix1_node.node_id)
            
            # Compute relative pose
            relative_pose_node = MODFGNode(
                node_id=f"{factor.factor_id}_relative_pose",
                operation_type=MOOperationType.ERROR_COMPUTE,
                input_shapes=[TensorShape(var1_dim, 1), TensorShape(var2_dim, 1)],
                output_shape=TensorShape(measurement_dim, 1),
                operation_params={'error_type': 'relative_pose_2d'}
            )
            mo_dfg.add_node(relative_pose_node)
            mo_dfg.add_edge(load_var1_node.node_id, relative_pose_node.node_id)
            mo_dfg.add_edge(load_var2_node.node_id, relative_pose_node.node_id)
            
            # Compute error
            error_node = MODFGNode(
                node_id=f"{factor.factor_id}_error",
                operation_type=MOOperationType.MATRIX_SUBTRACT,
                input_shapes=[TensorShape(measurement_dim, 1), TensorShape(measurement_dim, 1)],
                output_shape=TensorShape(measurement_dim, 1)
            )
            mo_dfg.add_node(error_node)
            mo_dfg.add_edge(relative_pose_node.node_id, error_node.node_id)
            mo_dfg.add_edge(load_measurement_node.node_id, error_node.node_id)
            
            # Compute Jacobians
            jacobian1_node = MODFGNode(
                node_id=f"{factor.factor_id}_jacobian_{var1_key}",
                operation_type=MOOperationType.JACOBIAN_COMPUTE,
                input_shapes=[TensorShape(var1_dim, 1), TensorShape(var2_dim, 1)],
                output_shape=TensorShape(measurement_dim, var1_dim),
                operation_params={'jacobian_wrt': var1_key, 'error_type': 'relative_pose_2d'}
            )
            mo_dfg.add_node(jacobian1_node)
            mo_dfg.add_edge(load_var1_node.node_id, jacobian1_node.node_id)
            mo_dfg.add_edge(load_var2_node.node_id, jacobian1_node.node_id)
            
            jacobian2_node = MODFGNode(
                node_id=f"{factor.factor_id}_jacobian_{var2_key}",
                operation_type=MOOperationType.JACOBIAN_COMPUTE,
                input_shapes=[TensorShape(var1_dim, 1), TensorShape(var2_dim, 1)],
                output_shape=TensorShape(measurement_dim, var2_dim),
                operation_params={'jacobian_wrt': var2_key, 'error_type': 'relative_pose_2d'}
            )
            mo_dfg.add_node(jacobian2_node)
            mo_dfg.add_edge(load_var1_node.node_id, jacobian2_node.node_id)
            mo_dfg.add_edge(load_var2_node.node_id, jacobian2_node.node_id)
            
            # Mark output nodes
            mo_dfg.mark_output_nodes([error_node.node_id, jacobian1_node.node_id, jacobian2_node.node_id])
    
    def _generate_unary_factor_dfg(self, factor: FactorNode, mo_dfg: MatrixOperationDFG):
        """Generate MO-DFG for unary factor (e.g., GPS measurement)"""
        # Similar to prior factor but may have different measurement model
        self._generate_prior_factor_dfg(factor, mo_dfg)
    
    def _generate_bearing_range_factor_dfg(self, factor: FactorNode, mo_dfg: MatrixOperationDFG):
        """Generate MO-DFG for bearing-range factor"""
        pose_key, landmark_key = factor.connected_variables[0], factor.connected_variables[1]
        pose_dim = self.variable_dimensions[pose_key]
        landmark_dim = self.variable_dimensions[landmark_key]
        measurement_dim = factor.get_measurement_dimension()
        
        # Load data
        load_pose_node = MODFGNode(
            node_id=f"{factor.factor_id}_load_{pose_key}",
            operation_type=MOOperationType.LOAD_DATA,
            input_shapes=[],
            output_shape=TensorShape(pose_dim, 1),
            operation_params={'variable_key': pose_key}
        )
        mo_dfg.add_node(load_pose_node)
        
        load_landmark_node = MODFGNode(
            node_id=f"{factor.factor_id}_load_{landmark_key}",
            operation_type=MOOperationType.LOAD_DATA,
            input_shapes=[],
            output_shape=TensorShape(landmark_dim, 1),
            operation_params={'variable_key': landmark_key}
        )
        mo_dfg.add_node(load_landmark_node)
        mo_dfg.mark_input_nodes([load_pose_node.node_id, load_landmark_node.node_id])
        
        # Compute predicted observation
        predicted_obs_node = MODFGNode(
            node_id=f"{factor.factor_id}_predicted_observation",
            operation_type=MOOperationType.ERROR_COMPUTE,
            input_shapes=[TensorShape(pose_dim, 1), TensorShape(landmark_dim, 1)],
            output_shape=TensorShape(measurement_dim, 1),
            operation_params={'error_type': 'bearing_range'}
        )
        mo_dfg.add_node(predicted_obs_node)
        mo_dfg.add_edge(load_pose_node.node_id, predicted_obs_node.node_id)
        mo_dfg.add_edge(load_landmark_node.node_id, predicted_obs_node.node_id)
        
        # Load measurement
        load_measurement_node = MODFGNode(
            node_id=f"{factor.factor_id}_load_measurement",
            operation_type=MOOperationType.LOAD_DATA,
            input_shapes=[],
            output_shape=TensorShape(measurement_dim, 1),
            operation_params={'data_type': 'measurement'}
        )
        mo_dfg.add_node(load_measurement_node)
        
        # Compute error
        error_node = MODFGNode(
            node_id=f"{factor.factor_id}_error",
            operation_type=MOOperationType.MATRIX_SUBTRACT,
            input_shapes=[TensorShape(measurement_dim, 1), TensorShape(measurement_dim, 1)],
            output_shape=TensorShape(measurement_dim, 1)
        )
        mo_dfg.add_node(error_node)
        mo_dfg.add_edge(predicted_obs_node.node_id, error_node.node_id)
        mo_dfg.add_edge(load_measurement_node.node_id, error_node.node_id)
        
        # Compute Jacobians
        jacobian_pose_node = MODFGNode(
            node_id=f"{factor.factor_id}_jacobian_{pose_key}",
            operation_type=MOOperationType.JACOBIAN_COMPUTE,
            input_shapes=[TensorShape(pose_dim, 1), TensorShape(landmark_dim, 1)],
            output_shape=TensorShape(measurement_dim, pose_dim),
            operation_params={'jacobian_wrt': pose_key, 'error_type': 'bearing_range'}
        )
        mo_dfg.add_node(jacobian_pose_node)
        mo_dfg.add_edge(load_pose_node.node_id, jacobian_pose_node.node_id)
        mo_dfg.add_edge(load_landmark_node.node_id, jacobian_pose_node.node_id)
        
        jacobian_landmark_node = MODFGNode(
            node_id=f"{factor.factor_id}_jacobian_{landmark_key}",
            operation_type=MOOperationType.JACOBIAN_COMPUTE,
            input_shapes=[TensorShape(pose_dim, 1), TensorShape(landmark_dim, 1)],
            output_shape=TensorShape(measurement_dim, landmark_dim),
            operation_params={'jacobian_wrt': landmark_key, 'error_type': 'bearing_range'}
        )
        mo_dfg.add_node(jacobian_landmark_node)
        mo_dfg.add_edge(load_pose_node.node_id, jacobian_landmark_node.node_id)
        mo_dfg.add_edge(load_landmark_node.node_id, jacobian_landmark_node.node_id)
        
        # Mark output nodes
        mo_dfg.mark_output_nodes([error_node.node_id, jacobian_pose_node.node_id, jacobian_landmark_node.node_id])
    
    def _generate_generic_factor_dfg(self, factor: FactorNode, mo_dfg: MatrixOperationDFG):
        """Generate MO-DFG for generic/custom factors"""
        # For custom factors, we rely on the provided error function and Jacobian expressions
        # This is a simplified implementation that would need to be extended
        # based on the specific mathematical expressions provided
        
        # Load all connected variables
        input_node_ids = []
        for var_key in factor.connected_variables:
            var_dim = self.variable_dimensions[var_key]
            load_node = MODFGNode(
                node_id=f"{factor.factor_id}_load_{var_key}",
                operation_type=MOOperationType.LOAD_DATA,
                input_shapes=[],
                output_shape=TensorShape(var_dim, 1),
                operation_params={'variable_key': var_key}
            )
            mo_dfg.add_node(load_node)
            input_node_ids.append(load_node.node_id)
        
        mo_dfg.mark_input_nodes(input_node_ids)
        
        # Generic error computation
        measurement_dim = factor.get_measurement_dimension()
        error_node = MODFGNode(
            node_id=f"{factor.factor_id}_error",
            operation_type=MOOperationType.ERROR_COMPUTE,
            input_shapes=[TensorShape(self.variable_dimensions[var], 1) 
                         for var in factor.connected_variables],
            output_shape=TensorShape(measurement_dim, 1),
            operation_params={'error_function': factor.error_function}
        )
        mo_dfg.add_node(error_node)
        
        # Connect all input nodes to error computation
        for input_node_id in input_node_ids:
            mo_dfg.add_edge(input_node_id, error_node.node_id)
        
        # Generic Jacobian computation for each variable
        jacobian_node_ids = []
        for var_key in factor.connected_variables:
            var_dim = self.variable_dimensions[var_key]
            jacobian_node = MODFGNode(
                node_id=f"{factor.factor_id}_jacobian_{var_key}",
                operation_type=MOOperationType.JACOBIAN_COMPUTE,
                input_shapes=[TensorShape(self.variable_dimensions[var], 1) 
                             for var in factor.connected_variables],
                output_shape=TensorShape(measurement_dim, var_dim),
                operation_params={
                    'jacobian_wrt': var_key,
                    'jacobian_expression': factor.jacobian_expressions.get(var_key, 'auto')
                }
            )
            mo_dfg.add_node(jacobian_node)
            jacobian_node_ids.append(jacobian_node.node_id)
            
            # Connect all input nodes to Jacobian computation
            for input_node_id in input_node_ids:
                mo_dfg.add_edge(input_node_id, jacobian_node.node_id)
        
        # Mark output nodes
        mo_dfg.mark_output_nodes([error_node.node_id] + jacobian_node_ids)
