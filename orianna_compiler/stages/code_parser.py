import ast
import inspect
import re
from typing import Dict, List, Any, Optional, Union, Tuple
import numpy as np

from ..core.factor_graph_ir import (
    FactorGraphIR, VariableNode, FactorNode, 
    VariableType, FactorType
)


class FactorGraphCodeParser:
    def __init__(self):
        self.factor_graph = FactorGraphIR()
        self.variable_registry: Dict[str, VariableNode] = {}
        self.factor_registry: Dict[str, FactorNode] = {}
        
    def parse_from_function(self, func) -> FactorGraphIR:
        # Get the source code of the function
        source = inspect.getsource(func)
        
        # Parse the AST
        tree = ast.parse(source)
        
        # Visit the AST and extract factor graph structure
        visitor = FactorGraphASTVisitor(self)
        visitor.visit(tree)
        
        # Validate the parsed factor graph
        if not self.factor_graph.validate():
            raise ValueError("Invalid factor graph structure")
            
        return self.factor_graph
    
    def parse_from_dict(self, graph_dict: Dict[str, Any]) -> FactorGraphIR:
        # Parse variables
        if 'variables' in graph_dict:
            for var_spec in graph_dict['variables']:
                self._parse_variable_spec(var_spec)
        
        # Parse factors
        if 'factors' in graph_dict:
            for factor_spec in graph_dict['factors']:
                self._parse_factor_spec(factor_spec)
        
        # Validate the parsed factor graph
        if not self.factor_graph.validate():
            raise ValueError("Invalid factor graph structure")
            
        return self.factor_graph
    
    def _parse_variable_spec(self, var_spec: Dict[str, Any]):
        """Parse a single variable specification"""
        key = var_spec['key']
        var_type_str = var_spec.get('type', 'custom')
        dimension = var_spec['dimension']
        initial_value = var_spec.get('initial_value', None)
        
        # Convert string to VariableType enum
        var_type = self._string_to_variable_type(var_type_str)
        
        # Create variable node
        variable = VariableNode(
            key=key,
            var_type=var_type,
            dimension=dimension,
            initial_value=np.array(initial_value) if initial_value else None
        )
        
        # Add to factor graph
        self.factor_graph.add_variable(variable)
        self.variable_registry[key] = variable
    
    def _parse_factor_spec(self, factor_spec: Dict[str, Any]):
        """Parse a single factor specification"""
        factor_id = factor_spec['id']
        factor_type_str = factor_spec.get('type', 'custom')
        connected_variables = factor_spec['variables']
        measurement = np.array(factor_spec['measurement'])
        noise_model = np.array(factor_spec['noise_model'])
        error_function = factor_spec.get('error_function', None)
        
        # Convert string to FactorType enum
        factor_type = self._string_to_factor_type(factor_type_str)
        
        # Create factor node
        factor = FactorNode(
            factor_id=factor_id,
            factor_type=factor_type,
            connected_variables=connected_variables,
            measurement=measurement,
            noise_model=noise_model,
            error_function=error_function
        )
        
        # Add Jacobian expressions if provided
        if 'jacobians' in factor_spec:
            for var_key, jacobian_expr in factor_spec['jacobians'].items():
                factor.add_jacobian_expression(var_key, jacobian_expr)
        
        # Add to factor graph
        self.factor_graph.add_factor(factor)
        self.factor_registry[factor_id] = factor
    
    def _string_to_variable_type(self, type_str: str) -> VariableType:
        """Convert string to VariableType enum"""
        type_mapping = {
            'pose_2d': VariableType.POSE_2D,
            'pose_3d': VariableType.POSE_3D,
            'point_2d': VariableType.POINT_2D,
            'point_3d': VariableType.POINT_3D,
            'velocity': VariableType.VELOCITY,
            'bias': VariableType.BIAS,
            'custom': VariableType.CUSTOM
        }
        return type_mapping.get(type_str.lower(), VariableType.CUSTOM)
    
    def _string_to_factor_type(self, type_str: str) -> FactorType:
        """Convert string to FactorType enum"""
        type_mapping = {
            'prior': FactorType.PRIOR,
            'between': FactorType.BETWEEN,
            'unary': FactorType.UNARY,
            'bearing_range': FactorType.BEARING_RANGE,
            'projection': FactorType.PROJECTION,
            'imu': FactorType.IMU,
            'custom': FactorType.CUSTOM
        }
        return type_mapping.get(type_str.lower(), FactorType.CUSTOM)


class FactorGraphASTVisitor(ast.NodeVisitor):
    def __init__(self, parser: FactorGraphCodeParser):
        self.parser = parser
        self.current_context = None
    
    def visit_FunctionDef(self, node):
        if node.name.startswith('define_factor_graph'):
            self.current_context = 'factor_graph'
        self.generic_visit(node)
    
    def visit_Call(self, node):
        if isinstance(node.func, ast.Attribute):
            if node.func.attr == 'add_variable':
                self._parse_add_variable_call(node)
            elif node.func.attr == 'add_factor':
                self._parse_add_factor_call(node)
        elif isinstance(node.func, ast.Name):
            if node.func.id in ['Variable', 'Factor']:
                # Handle direct Variable/Factor constructor calls
                pass
        
        self.generic_visit(node)
    
    def _parse_add_variable_call(self, node):
        # Extract arguments from the function call
        # This is a simplified implementation - in practice, you'd need
        # more sophisticated argument parsing
        pass
    
    def _parse_add_factor_call(self, node):
        """Parse add_factor function call"""
        # Extract arguments from the function call
        # This is a simplified implementation
        pass


class CodeParser:
    """
    Main code parser interface for Orianna Compiler
    
    This class provides a unified interface for parsing different types
    of factor graph specifications.
    """
    
    def __init__(self):
        self.fg_parser = FactorGraphCodeParser()
    
    def parse(self, source: Union[str, Dict, callable]) -> FactorGraphIR:
        """
        Parse factor graph from various source types
        
        Args:
            source: Factor graph specification (function, dict, or string)
            
        Returns:
            FactorGraphIR: Parsed factor graph representation
        """
        if callable(source):
            return self.fg_parser.parse_from_function(source)
        elif isinstance(source, dict):
            return self.fg_parser.parse_from_dict(source)
        elif isinstance(source, str):
            # Try to parse as JSON/YAML first, then as Python code
            try:
                import json
                graph_dict = json.loads(source)
                return self.fg_parser.parse_from_dict(graph_dict)
            except json.JSONDecodeError:
                # Try parsing as Python code
                return self._parse_from_string(source)
        else:
            raise ValueError(f"Unsupported source type: {type(source)}")
    
    def _parse_from_string(self, code_str: str) -> FactorGraphIR:
        """Parse factor graph from Python code string"""
        # Compile and execute the code string
        namespace = {}
        exec(code_str, namespace)
        
        # Look for factor graph definition function
        for name, obj in namespace.items():
            if callable(obj) and name.startswith('define_factor_graph'):
                return self.fg_parser.parse_from_function(obj)
        
        raise ValueError("No factor graph definition found in code string")
    
    def create_example_slam_graph(self) -> FactorGraphIR:
        """Create an example SLAM factor graph for testing"""
        graph_dict = {
            'variables': [
                {
                    'key': 'x0',
                    'type': 'pose_2d',
                    'dimension': 3,
                    'initial_value': [0.0, 0.0, 0.0]
                },
                {
                    'key': 'x1',
                    'type': 'pose_2d', 
                    'dimension': 3,
                    'initial_value': [1.0, 0.0, 0.0]
                },
                {
                    'key': 'l0',
                    'type': 'point_2d',
                    'dimension': 2,
                    'initial_value': [2.0, 1.0]
                }
            ],
            'factors': [
                {
                    'id': 'prior_x0',
                    'type': 'prior',
                    'variables': ['x0'],
                    'measurement': [0.0, 0.0, 0.0],
                    'noise_model': [[0.1, 0.0, 0.0], [0.0, 0.1, 0.0], [0.0, 0.0, 0.1]],
                    'error_function': 'x0 - measurement'
                },
                {
                    'id': 'odom_x0_x1',
                    'type': 'between',
                    'variables': ['x0', 'x1'],
                    'measurement': [1.0, 0.0, 0.0],
                    'noise_model': [[0.2, 0.0, 0.0], [0.0, 0.2, 0.0], [0.0, 0.0, 0.1]],
                    'error_function': 'relative_pose(x0, x1) - measurement'
                },
                {
                    'id': 'obs_x1_l0',
                    'type': 'bearing_range',
                    'variables': ['x1', 'l0'],
                    'measurement': [1.0, 1.0],
                    'noise_model': [[0.1, 0.0], [0.0, 0.1]],
                    'error_function': 'observe(x1, l0) - measurement'
                }
            ]
        }
        
        return self.fg_parser.parse_from_dict(graph_dict)
