"""
SLAM Example for <PERSON><PERSON><PERSON> Compiler

This example demonstrates how to use the Orianna compiler to compile
a simple 2D SLAM factor graph into executable instructions.
"""

import numpy as np
import sys
import os

# Add parent directory to path to import orianna_compiler
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from orianna_compiler.compiler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from orianna_compiler.core.factor_graph_ir import (
    FactorGraphIR, VariableNode, FactorNode, 
    VariableType, FactorType
)


def create_simple_slam_graph():
    """Create a simple 2D SLAM factor graph"""
    
    # Define factor graph using dictionary specification
    slam_graph_spec = {
        'variables': [
            # Robot poses
            {
                'key': 'x0',
                'type': 'pose_2d',
                'dimension': 3,
                'initial_value': [0.0, 0.0, 0.0]  # [x, y, theta]
            },
            {
                'key': 'x1', 
                'type': 'pose_2d',
                'dimension': 3,
                'initial_value': [1.0, 0.0, 0.0]
            },
            {
                'key': 'x2',
                'type': 'pose_2d', 
                'dimension': 3,
                'initial_value': [2.0, 1.0, 0.5]
            },
            # Landmarks
            {
                'key': 'l0',
                'type': 'point_2d',
                'dimension': 2,
                'initial_value': [1.5, 1.5]  # [x, y]
            },
            {
                'key': 'l1',
                'type': 'point_2d',
                'dimension': 2, 
                'initial_value': [2.5, 0.5]
            }
        ],
        'factors': [
            # Prior factor on first pose
            {
                'id': 'prior_x0',
                'type': 'prior',
                'variables': ['x0'],
                'measurement': [0.0, 0.0, 0.0],
                'noise_model': [[0.1, 0.0, 0.0], [0.0, 0.1, 0.0], [0.0, 0.0, 0.1]],
                'error_function': 'x0 - measurement'
            },
            # Odometry factors
            {
                'id': 'odom_x0_x1',
                'type': 'between',
                'variables': ['x0', 'x1'],
                'measurement': [1.0, 0.0, 0.0],  # Move 1m forward
                'noise_model': [[0.2, 0.0, 0.0], [0.0, 0.2, 0.0], [0.0, 0.0, 0.1]],
                'error_function': 'relative_pose(x0, x1) - measurement'
            },
            {
                'id': 'odom_x1_x2',
                'type': 'between',
                'variables': ['x1', 'x2'],
                'measurement': [1.0, 1.0, 0.5],  # Move forward and turn
                'noise_model': [[0.2, 0.0, 0.0], [0.0, 0.2, 0.0], [0.0, 0.0, 0.1]],
                'error_function': 'relative_pose(x1, x2) - measurement'
            },
            # Landmark observation factors
            {
                'id': 'obs_x1_l0',
                'type': 'bearing_range',
                'variables': ['x1', 'l0'],
                'measurement': [0.7071, 1.4142],  # [bearing, range] to landmark l0
                'noise_model': [[0.1, 0.0], [0.0, 0.1]],
                'error_function': 'observe(x1, l0) - measurement'
            },
            {
                'id': 'obs_x2_l0',
                'type': 'bearing_range',
                'variables': ['x2', 'l0'],
                'measurement': [0.5, 1.0],
                'noise_model': [[0.1, 0.0], [0.0, 0.1]],
                'error_function': 'observe(x2, l0) - measurement'
            },
            {
                'id': 'obs_x2_l1',
                'type': 'bearing_range',
                'variables': ['x2', 'l1'],
                'measurement': [0.0, 1.0],
                'noise_model': [[0.1, 0.0], [0.0, 0.1]],
                'error_function': 'observe(x2, l1) - measurement'
            }
        ]
    }
    
    return slam_graph_spec


def create_slam_graph_from_function():
    """Create SLAM factor graph using function-based definition"""
    
    def define_factor_graph_slam():
        """Factor graph definition function"""
        # This is a placeholder for function-based definition
        # In practice, this would use a DSL for factor graph specification
        pass
    
    return define_factor_graph_slam


def main():
    """Main example execution"""
    print("Orianna Compiler SLAM Example")
    print("=" * 40)
    
    # Initialize compiler
    compiler = OriannaCompiler(enable_logging=True)
    
    # Create SLAM factor graph
    print("\n1. Creating SLAM factor graph...")
    slam_graph = create_simple_slam_graph()
    
    # Set optimization parameters
    optimization_params = {
        'max_iterations': 10,
        'tolerance': 1e-6,
        'damping_factor': 1e-3,
        'use_levenberg_marquardt': True
    }
    
    # Compile the factor graph
    print("\n2. Compiling factor graph...")
    result = compiler.compile(slam_graph, optimization_params)
    
    if result.success:
        print("✓ Compilation successful!")
        
        # Print compilation report
        print("\n3. Compilation Report:")
        print(compiler.get_compilation_report())
        
        # Generate EDS-compatible configuration
        print("\n4. Generating Event-Driven-Simulator configuration...")
        try:
            eds_config = compiler.compile_to_eds_format(slam_graph, optimization_params)
            print("✓ EDS configuration generated successfully!")
            print(f"   Total instructions: {len(eds_config['instruction_list'])}")
            print(f"   Variables: {eds_config['model_param']['num_variables']}")
            print(f"   Factors: {eds_config['model_param']['num_factors']}")
        except Exception as e:
            print(f"✗ EDS configuration generation failed: {e}")
        
        # Display instruction breakdown
        print("\n5. Instruction Breakdown:")
        forward_ops = {}
        backward_ops = {}
        inference_ops = {}
        
        for op, _ in result.forward_instructions:
            forward_ops[op.type] = forward_ops.get(op.type, 0) + 1
        
        for op, _ in result.backward_instructions:
            backward_ops[op.type] = backward_ops.get(op.type, 0) + 1
            
        for op, _ in result.inference_instructions:
            inference_ops[op.type] = inference_ops.get(op.type, 0) + 1
        
        print(f"   Forward pass operations: {forward_ops}")
        print(f"   Backward pass operations: {backward_ops}")
        print(f"   Total inference operations: {inference_ops}")
        
    else:
        print(f"✗ Compilation failed: {result.error_message}")
        return 1
    
    print("\n6. Example completed successfully!")
    return 0


def test_compiler_stages():
    """Test individual compiler stages"""
    print("\nTesting Individual Compiler Stages")
    print("=" * 40)
    
    compiler = OriannaCompiler(enable_logging=False)
    slam_graph = create_simple_slam_graph()
    
    try:
        # Test Stage 1: Code Parsing
        print("Testing Stage 1: Code Parsing...")
        factor_graph = compiler.code_parser.parse(slam_graph)
        print(f"✓ Parsed {len(factor_graph.variables)} variables and {len(factor_graph.factors)} factors")
        
        # Test Stage 2: MO-DFG Generation
        print("Testing Stage 2: MO-DFG Generation...")
        mo_dfgs = compiler.mo_dfg_generator.generate(factor_graph)
        print(f"✓ Generated {len(mo_dfgs)} MO-DFGs")
        
        # Test Stage 3: Forward Traversal
        print("Testing Stage 3: Forward Traversal...")
        forward_instructions = compiler.forward_traversal.generate_rhs_instructions(factor_graph, mo_dfgs)
        print(f"✓ Generated {len(forward_instructions)} forward instructions")
        
        # Test Stage 4: Backward Propagation
        print("Testing Stage 4: Backward Propagation...")
        backward_instructions = compiler.backward_propagation.generate_coefficient_matrix_instructions(factor_graph, mo_dfgs)
        print(f"✓ Generated {len(backward_instructions)} backward instructions")
        
        # Test Stage 5: Inference Generation
        print("Testing Stage 5: Inference Generation...")
        inference_instructions = compiler.inference_generator.generate_inference_instructions(
            factor_graph, mo_dfgs, forward_instructions, backward_instructions
        )
        print(f"✓ Generated {len(inference_instructions)} inference instructions")
        
        print("✓ All stages tested successfully!")
        
    except Exception as e:
        print(f"✗ Stage testing failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    # Run main example
    exit_code = main()
    
    if exit_code == 0:
        # Run stage tests
        exit_code = test_compiler_stages()
    
    exit(exit_code)
