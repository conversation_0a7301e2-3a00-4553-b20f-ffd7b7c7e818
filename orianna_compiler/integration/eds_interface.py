"""
Event-Driven-Simulator Integration Interface

This module provides integration between the Orianna compiler and the
existing Event-Driven-Simulator framework, allowing compiled factor graphs
to be executed on the EDS platform.
"""

import os
import sys
from typing import Dict, List, Any, Optional, Tuple

# Add the main project directory to path for imports
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from mapping_scheduling.op_list import Operator, OpList
from mapping_scheduling.scheduling_utils import merge_sub_instr_lists, print_statistics
from utils.instruction_queue import Instruction
from orianna_compiler.compiler import <PERSON><PERSON><PERSON>Compile<PERSON>, CompilationResult


class OriannaEDSInterface:
    """
    Interface between Orianna Compiler and Event-Driven-Simulator
    
    This class handles the conversion of Orianna compilation results
    into formats compatible with the EDS execution framework.
    """
    
    def __init__(self):
        self.compiler = OriannaCompiler(enable_logging=True)
        self.last_compilation_result: Optional[CompilationResult] = None
        
    def compile_slam_factor_graph(self, 
                                 factor_graph_spec: Dict[str, Any],
                                 optimization_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Compile SLAM factor graph and return EDS-compatible configuration
        
        Args:
            factor_graph_spec: Factor graph specification dictionary
            optimization_params: Optimization parameters
            
        Returns:
            EDS-compatible configuration dictionary
        """
        # Set default optimization parameters
        if optimization_params is None:
            optimization_params = {
                'max_iterations': 10,
                'tolerance': 1e-6,
                'damping_factor': 1e-3,
                'use_levenberg_marquardt': True
            }
        
        # Compile using Orianna
        result = self.compiler.compile(factor_graph_spec, optimization_params)
        self.last_compilation_result = result
        
        if not result.success:
            raise RuntimeError(f"Orianna compilation failed: {result.error_message}")
        
        # Convert to EDS format
        return self._convert_to_eds_format(result, optimization_params)
    
    def _convert_to_eds_format(self, result: CompilationResult, 
                              optimization_params: Dict[str, Any]) -> Dict[str, Any]:
        """Convert compilation result to EDS format"""
        
        # Generate sub-instruction lists in EDS format
        sub_lists = self._generate_eds_sub_lists(result)
        
        # Generate EDS-compatible configuration
        eds_config = {
            'scheduling_setting': {
                'module_for_StaticVMM': 'acim_l',
                'module_for_DynamicVMM': 'dcim',
                'check_rram_reuse': False
            },
            'input_row_parallel_dict': self._generate_parallelism_dict(result),
            'param': self._generate_model_parameters(result),
            'sub_lists': sub_lists,
            'optimization_params': optimization_params,
            'compilation_stats': result.statistics
        }
        
        return eds_config
    
    def _generate_eds_sub_lists(self, result: CompilationResult) -> List[Dict[str, Any]]:
        """Generate EDS-compatible sub-instruction lists"""
        sub_lists = []
        
        # Convert Orianna instructions to EDS format
        eds_instructions = []
        
        # Add all inference instructions
        for i, (op, mapping) in enumerate(result.inference_instructions):
            eds_instruction = [op, mapping]
            eds_instructions.append(eds_instruction)
        
        # Create sub-list structure
        sub_list = {
            'i_list': eds_instructions,
            'original_len': len(eds_instructions),
            'input_row_reuse': 1
        }
        
        sub_lists.append(sub_list)
        
        return sub_lists
    
    def _generate_parallelism_dict(self, result: CompilationResult) -> Dict[str, int]:
        """Generate input row parallelism dictionary"""
        parallelism_dict = {}
        
        # Set parallelism for each factor
        for factor_id in result.factor_graph.factors.keys():
            parallelism_dict[f'{factor_id}_input'] = 1
        
        # Add standard SLAM parallelism settings
        parallelism_dict.update({
            'deltaRij_input': 1,
            'phii_input': 1,
            'phij_input': 1,
            'ti_input': 1,
            'tj_input': 1,
            'deltat_ij_input': 1,
            'A_matrix_input': 1,
        })
        
        return parallelism_dict
    
    def _generate_model_parameters(self, result: CompilationResult) -> Dict[str, Any]:
        """Generate model parameters for EDS"""
        stats = result.factor_graph.get_statistics()
        
        # Determine dimensions based on variable types
        max_pose_dim = 3  # Default for 2D poses
        max_measurement_dim = 3  # Default measurement dimension
        
        for variable in result.factor_graph.variables.values():
            if variable.var_type.value in ['pose_2d']:
                max_pose_dim = max(max_pose_dim, 3)
            elif variable.var_type.value in ['pose_3d']:
                max_pose_dim = max(max_pose_dim, 6)
        
        return {
            'num_variables': stats['num_variables'],
            'num_factors': stats['num_factors'],
            'total_variable_dimension': stats['total_variable_dimension'],
            'total_measurement_dimension': stats['total_measurement_dimension'],
            'matrix_dim': max_pose_dim,
            'translation_dim': max_pose_dim,
            'rotation_dim': max_pose_dim,
            'M_dim': stats['total_variable_dimension'],
            'variable_types': stats['variable_types'],
            'factor_types': stats['factor_types']
        }
    
    def create_eds_scheduling_config(self, factor_graph_spec: Dict[str, Any],
                                   optimization_params: Optional[Dict[str, Any]] = None) -> callable:
        """
        Create EDS scheduling configuration function
        
        Args:
            factor_graph_spec: Factor graph specification
            optimization_params: Optimization parameters
            
        Returns:
            Function compatible with EDS auto_scheduling system
        """
        # Compile the factor graph
        eds_config = self.compile_slam_factor_graph(factor_graph_spec, optimization_params)
        
        def get_scheduling_configs(scheduling_setting: Optional[dict] = None,
                                 input_row_parallel_dict: Optional[dict] = None,
                                 param: Optional[dict] = None):
            """EDS-compatible scheduling configuration function"""
            
            # Use provided settings or defaults from compilation
            if scheduling_setting is None:
                scheduling_setting = eds_config['scheduling_setting']
            
            if input_row_parallel_dict is None:
                input_row_parallel_dict = eds_config['input_row_parallel_dict']
            
            if param is None:
                param = eds_config['param']
            
            # Return the sub-lists and statistics
            instruction_list, statistics_dict = merge_sub_instr_lists(eds_config['sub_lists'])
            print_statistics(statistics_dict)
            
            return instruction_list, statistics_dict
        
        return get_scheduling_configs
    
    def integrate_with_existing_slam_config(self, 
                                          factor_graph_spec: Dict[str, Any],
                                          base_config_name: str = 'slam_factorgraph') -> str:
        """
        Integrate Orianna-compiled factor graph with existing SLAM configuration
        
        Args:
            factor_graph_spec: Factor graph specification
            base_config_name: Name of base SLAM configuration to extend
            
        Returns:
            Name of the new integrated configuration
        """
        # Compile the factor graph
        eds_config = self.compile_slam_factor_graph(factor_graph_spec)
        
        # Generate new configuration file
        config_name = f'{base_config_name}_orianna'
        config_file_path = f'mapping_scheduling/configs/{config_name}.py'
        
        # Create configuration file content
        config_content = self._generate_config_file_content(eds_config, base_config_name)
        
        # Write configuration file
        with open(config_file_path, 'w') as f:
            f.write(config_content)
        
        return config_name
    
    def _generate_config_file_content(self, eds_config: Dict[str, Any], 
                                    base_config_name: str) -> str:
        """Generate EDS configuration file content"""
        
        content = f'''"""
{base_config_name}_orianna.py - Generated by Orianna Compiler

This configuration file was automatically generated by the Orianna compiler
for SLAM factor graph optimization. It contains the compiled instruction
sequences for the specified factor graph.
"""

from typing import Optional
import numpy as np
import networkx as nx
from mapping_scheduling.op_list import Operator, OpList
from mapping_scheduling.scheduling_utils import merge_sub_instr_lists, print_statistics
from utils.utils import deepcopy_with_pickle

def get_scheduling_configs(scheduling_setting: Optional[dict]=None, 
                         input_row_parallel_dict: Optional[dict]=None, 
                         param: Optional[dict]=None):
    """
    Get scheduling configuration for Orianna-compiled SLAM factor graph
    
    Generated by Orianna Compiler
    Factor Graph Statistics:
    - Variables: {eds_config['param']['num_variables']}
    - Factors: {eds_config['param']['num_factors']}
    - Total Variable Dimension: {eds_config['param']['total_variable_dimension']}
    - Total Measurement Dimension: {eds_config['param']['total_measurement_dimension']}
    """
    
    if scheduling_setting is None:
        scheduling_setting = {eds_config['scheduling_setting']}
    
    if input_row_parallel_dict is None:
        input_row_parallel_dict = {eds_config['input_row_parallel_dict']}
    
    if param is None:
        param = {eds_config['param']}
    
    # Generated instruction lists
    sub_lists = {eds_config['sub_lists']}
    
    # Merge and return instruction lists
    instruction_list, statistics_dict = merge_sub_instr_lists(sub_lists)
    print_statistics(statistics_dict)
    
    return instruction_list, statistics_dict
'''
        
        return content
    
    def get_compilation_summary(self) -> str:
        """Get summary of last compilation"""
        if not self.last_compilation_result:
            return "No compilation results available"
        
        result = self.last_compilation_result
        
        if not result.success:
            return f"Last compilation failed: {result.error_message}"
        
        return f"""
Orianna-EDS Integration Summary
==============================

Compilation Status: SUCCESS
Compilation Time: {result.compilation_time:.3f} seconds

Factor Graph:
- Variables: {result.statistics['factor_graph_stats']['num_variables']}
- Factors: {result.statistics['factor_graph_stats']['num_factors']}
- Total Instructions Generated: {len(result.inference_instructions)}

EDS Integration:
- Compatible with Event-Driven-Simulator framework
- Generated {len(result.inference_instructions)} executable instructions
- Ready for hardware execution and performance analysis
"""


# Convenience function for direct integration
def compile_slam_for_eds(factor_graph_spec: Dict[str, Any],
                        optimization_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Convenience function to compile SLAM factor graph for EDS execution
    
    Args:
        factor_graph_spec: Factor graph specification dictionary
        optimization_params: Optimization parameters
        
    Returns:
        EDS-compatible configuration dictionary
    """
    interface = OriannaEDSInterface()
    return interface.compile_slam_factor_graph(factor_graph_spec, optimization_params)


# Example usage function
def create_example_eds_integration():
    """Create an example EDS integration"""
    
    # Example SLAM factor graph
    example_graph = {
        'variables': [
            {'key': 'x0', 'type': 'pose_2d', 'dimension': 3, 'initial_value': [0.0, 0.0, 0.0]},
            {'key': 'x1', 'type': 'pose_2d', 'dimension': 3, 'initial_value': [1.0, 0.0, 0.0]},
            {'key': 'l0', 'type': 'point_2d', 'dimension': 2, 'initial_value': [1.5, 1.5]}
        ],
        'factors': [
            {
                'id': 'prior_x0', 'type': 'prior', 'variables': ['x0'],
                'measurement': [0.0, 0.0, 0.0],
                'noise_model': [[0.1, 0.0, 0.0], [0.0, 0.1, 0.0], [0.0, 0.0, 0.1]]
            },
            {
                'id': 'odom_x0_x1', 'type': 'between', 'variables': ['x0', 'x1'],
                'measurement': [1.0, 0.0, 0.0],
                'noise_model': [[0.2, 0.0, 0.0], [0.0, 0.2, 0.0], [0.0, 0.0, 0.1]]
            },
            {
                'id': 'obs_x1_l0', 'type': 'bearing_range', 'variables': ['x1', 'l0'],
                'measurement': [0.7071, 1.4142],
                'noise_model': [[0.1, 0.0], [0.0, 0.1]]
            }
        ]
    }
    
    # Compile for EDS
    interface = OriannaEDSInterface()
    eds_config = interface.compile_slam_factor_graph(example_graph)
    
    print("Example EDS integration created successfully!")
    print(interface.get_compilation_summary())
    
    return eds_config
