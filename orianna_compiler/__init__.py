"""
<PERSON><PERSON><PERSON>mpiler for SLAM Factor Graph Optimization

A specialized compiler that transforms user-defined factor graph programs 
into executable low-level matrix operation instruction sequences for 
the Event-Driven-Simulator framework.

Five-stage compilation pipeline:
1. Code Parsing: Extract factor graph structure from user code
2. MO-DFG Generation: Generate matrix operation data flow graphs for each factor
3. Forward Traversal: Generate instructions for building RHS vector b
4. Backward Propagation: Generate instructions for building coefficient matrix A
5. Inference Instruction Generation: Generate factor graph inference and optimization instructions
"""

from .core.factor_graph_ir import FactorGraphIR, VariableNode, FactorNode
from .core.mo_dfg import MatrixOperationDFG, MODFGNode
from .stages.code_parser import CodeParser
from .stages.mo_dfg_generator import MODFGGenerator
from .stages.forward_traversal import ForwardTraversal
from .stages.backward_propagation import BackwardPropagation
from .stages.inference_generator import InferenceGenerator
from .compiler import OriannaCompiler

__version__ = "1.0.0"
__author__ = "SLAM Optimization Team"

__all__ = [
    '<PERSON><PERSON>naCompiler',
    'FactorGraphIR',
    'VariableNode', 
    'FactorNode',
    'MatrixOperationDFG',
    '<PERSON>OD<PERSON><PERSON>N<PERSON>',
    'CodeParser',
    'MODFGGenerator',
    'ForwardTraversal',
    'BackwardPropagation',
    'InferenceGenerator'
]
