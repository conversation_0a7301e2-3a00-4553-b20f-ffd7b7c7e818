import numpy as np
from typing import List, Dict, Any, <PERSON><PERSON>, <PERSON><PERSON>
from enum import Enum
import networkx as nx


class VariableType(Enum):
    POSE_2D = "pose_2d"           # 2D pose (x, y, theta)
    POSE_3D = "pose_3d"           # 3D pose (x, y, z, qx, qy, qz, qw)
    POINT_2D = "point_2d"         # 2D landmark point
    POINT_3D = "point_3d"         # 3D landmark point
    VELOCITY = "velocity"         # Velocity vector
    BIAS = "bias"                 # IMU bias
    CUSTOM = "custom"             # User-defined variable type


class FactorType(Enum):
    PRIOR = "prior"                  # Prior factor
    BETWEEN = "between"              # Between factor (odometry)
    UNARY = "unary"                  # Unary factor (GPS, etc.)
    BEARING_RANGE = "bearing_range"  # Bearing-range factor
    PROJECTION = "projection"        # Camera projection factor
    IMU = "imu"                      # IMU factor
    CUSTOM = "custom"                # User-defined factor type


class VariableNode:
    def __init__(self, key: str, var_type: VariableType, dimension: int, initial_value: Optional[np.ndarray] = None):
        self.key = key
        self.var_type = var_type
        self.dimension = dimension
        self.initial_value = initial_value if initial_value is not None else np.zeros(dimension)
        self.connected_factors: List['FactorNode'] = []
        
    def add_factor(self, factor: 'FactorNode'):
        if factor not in self.connected_factors:
            self.connected_factors.append(factor)
    
    def remove_factor(self, factor: 'FactorNode'):
        if factor in self.connected_factors:
            self.connected_factors.remove(factor)
    
    def __str__(self):
        return f"Variable({self.key}, {self.var_type.value}, dim={self.dimension})"


class FactorNode:  
    def __init__(self, factor_id: str, factor_type: FactorType, 
                 connected_variables: List[str], measurement: np.ndarray,
                 noise_model: np.ndarray, error_function: str = None):
        """
        Initialize a factor node
        
        Args:
            factor_id: Unique identifier for the factor
            factor_type: Type of the factor
            connected_variables: List of variable keys this factor connects
            measurement: Measurement vector
            noise_model: Noise covariance matrix or information matrix
            error_function: Mathematical expression for error function
        """
        self.factor_id = factor_id
        self.factor_type = factor_type
        self.connected_variables = connected_variables
        self.measurement = measurement
        self.noise_model = noise_model
        self.error_function = error_function
        self.jacobian_expressions: Dict[str, str] = {} 
        
    def add_jacobian_expression(self, variable_key: str, jacobian_expr: str):
        self.jacobian_expressions[variable_key] = jacobian_expr
    
    def get_measurement_dimension(self) -> int:
        return len(self.measurement) if self.measurement is not None else 0
    
    def __str__(self):
        return f"Factor({self.factor_id}, {self.factor_type.value}, vars={self.connected_variables})"


class FactorGraphIR:
    def __init__(self):
        self.variables: Dict[str, VariableNode] = {}
        self.factors: Dict[str, FactorNode] = {}
        self.graph = nx.Graph()  # NetworkX graph for topology analysis
        
    def add_variable(self, variable: VariableNode):
        self.variables[variable.key] = variable
        self.graph.add_node(variable.key, node_type='variable', node_obj=variable)
    
    def add_factor(self, factor: FactorNode):
        self.factors[factor.factor_id] = factor
        self.graph.add_node(factor.factor_id, node_type='factor', node_obj=factor)
        
        # Add edges between factor and connected variables
        for var_key in factor.connected_variables:
            if var_key in self.variables:
                self.graph.add_edge(factor.factor_id, var_key)
                self.variables[var_key].add_factor(factor)
    
    def remove_variable(self, variable_key: str):
        if variable_key in self.variables:
            variable = self.variables[variable_key]
            # Remove all connected factors
            for factor in variable.connected_factors[:]:
                self.remove_factor(factor.factor_id)
            # Remove variable
            del self.variables[variable_key]
            self.graph.remove_node(variable_key)
    
    def remove_factor(self, factor_id: str):
        if factor_id in self.factors:
            factor = self.factors[factor_id]
            # Update connected variables
            for var_key in factor.connected_variables:
                if var_key in self.variables:
                    self.variables[var_key].remove_factor(factor)
            # Remove factor
            del self.factors[factor_id]
            self.graph.remove_node(factor_id)
    
    def get_variable_ordering(self) -> List[str]:
        return list(self.variables.keys())
    
    def get_factor_ordering(self) -> List[str]:
        return list(self.factors.keys())
    
    def get_total_variable_dimension(self) -> int:
        return sum(var.dimension for var in self.variables.values())
    
    def get_total_measurement_dimension(self) -> int:
        return sum(factor.get_measurement_dimension() for factor in self.factors.values())
    
    def validate(self) -> bool:
        # Check that all factor-connected variables exist
        for factor in self.factors.values():
            for var_key in factor.connected_variables:
                if var_key not in self.variables:
                    return False
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        return {
            'num_variables': len(self.variables),
            'num_factors': len(self.factors),
            'total_variable_dimension': self.get_total_variable_dimension(),
            'total_measurement_dimension': self.get_total_measurement_dimension(),
            'variable_types': {vtype.value: sum(1 for v in self.variables.values() 
                                              if v.var_type == vtype) 
                              for vtype in VariableType},
            'factor_types': {ftype.value: sum(1 for f in self.factors.values() 
                                            if f.factor_type == ftype) 
                            for ftype in FactorType}
        }
    
    def __str__(self):
        stats = self.get_statistics()
        return f"FactorGraphIR(vars={stats['num_variables']}, factors={stats['num_factors']})"
