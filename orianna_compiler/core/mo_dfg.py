"""
Matrix Operation Data Flow Graph (MO-DFG)

This module defines the data flow graph representation for matrix operations
generated from factor graph analysis. Each factor node in the factor graph
corresponds to a MO-DFG that describes the matrix computations needed.
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Set
from enum import Enum
import networkx as nx
from dataclasses import dataclass


class MOOperationType(Enum):
    """Types of matrix operations in MO-DFG"""
    # Basic matrix operations
    MATRIX_MULTIPLY = "matrix_multiply"      # A * B
    MATRIX_ADD = "matrix_add"               # A + B
    MATRIX_SUBTRACT = "matrix_subtract"     # A - B
    TRANSPOSE = "transpose"                 # A^T
    INVERSE = "inverse"                     # A^(-1)
    
    # SLAM-specific operations
    EXPONENTIAL_MAP = "exponential_map"     # exp(phi) -> R (SO(3) exponential map)
    LOGARITHM_MAP = "logarithm_map"         # log(R) -> phi (SO(3) logarithm map)
    SKEW_SYMMETRIC = "skew_symmetric"       # [phi]_× (skew-symmetric matrix)
    
    # Jacobian operations
    JACOBIAN_COMPUTE = "jacobian_compute"   # Compute Jacobian matrix
    ERROR_COMPUTE = "error_compute"         # Compute error vector
    
    # Linear algebra operations
    QR_DECOMPOSITION = "qr_decomposition"   # QR decomposition
    CHOLESKY = "cholesky"                   # Cholesky decomposition
    BACK_SUBSTITUTION = "back_substitution" # Back substitution
    
    # Data operations
    LOAD_DATA = "load_data"                 # Load data from memory
    STORE_DATA = "store_data"               # Store data to memory
    COPY_DATA = "copy_data"                 # Copy data


@dataclass
class TensorShape:
    """Represents the shape of a tensor/matrix"""
    rows: int
    cols: int
    
    def __post_init__(self):
        if self.rows <= 0 or self.cols <= 0:
            raise ValueError("Tensor dimensions must be positive")
    
    @property
    def size(self) -> int:
        return self.rows * self.cols
    
    def is_vector(self) -> bool:
        return self.cols == 1
    
    def is_square(self) -> bool:
        return self.rows == self.cols
    
    def __str__(self):
        return f"{self.rows}x{self.cols}"


class MODFGNode:
    """Represents a node in the Matrix Operation Data Flow Graph"""
    
    def __init__(self, node_id: str, operation_type: MOOperationType,
                 input_shapes: List[TensorShape], output_shape: TensorShape,
                 operation_params: Optional[Dict[str, Any]] = None):
        """
        Initialize a MO-DFG node
        
        Args:
            node_id: Unique identifier for the node
            operation_type: Type of matrix operation
            input_shapes: List of input tensor shapes
            output_shape: Output tensor shape
            operation_params: Additional parameters for the operation
        """
        self.node_id = node_id
        self.operation_type = operation_type
        self.input_shapes = input_shapes
        self.output_shape = output_shape
        self.operation_params = operation_params or {}
        
        # Dependencies
        self.input_nodes: List['MODFGNode'] = []
        self.output_nodes: List['MODFGNode'] = []
        
        # Execution information
        self.execution_order: Optional[int] = None
        self.is_scheduled: bool = False
        
        # Mapping to Event-Driven-Simulator operations
        self.mapped_operations: List[str] = []  # List of EDS operation types
        
    def add_input_node(self, node: 'MODFGNode'):
        """Add an input dependency node"""
        if node not in self.input_nodes:
            self.input_nodes.append(node)
            node.output_nodes.append(self)
    
    def add_output_node(self, node: 'MODFGNode'):
        """Add an output dependency node"""
        if node not in self.output_nodes:
            self.output_nodes.append(node)
            node.input_nodes.append(self)
    
    def remove_input_node(self, node: 'MODFGNode'):
        """Remove an input dependency node"""
        if node in self.input_nodes:
            self.input_nodes.remove(node)
            node.output_nodes.remove(self)
    
    def remove_output_node(self, node: 'MODFGNode'):
        """Remove an output dependency node"""
        if node in self.output_nodes:
            self.output_nodes.remove(node)
            node.input_nodes.remove(self)
    
    def get_computational_complexity(self) -> int:
        """Estimate computational complexity (FLOPs)"""
        if self.operation_type == MOOperationType.MATRIX_MULTIPLY:
            if len(self.input_shapes) >= 2:
                m, k = self.input_shapes[0].rows, self.input_shapes[0].cols
                n = self.input_shapes[1].cols
                return 2 * m * k * n  # 2 * m * k * n FLOPs for matrix multiplication
        elif self.operation_type in [MOOperationType.MATRIX_ADD, MOOperationType.MATRIX_SUBTRACT]:
            return self.output_shape.size
        elif self.operation_type == MOOperationType.TRANSPOSE:
            return 0  # Transpose is essentially free (just indexing)
        elif self.operation_type == MOOperationType.QR_DECOMPOSITION:
            m, n = self.input_shapes[0].rows, self.input_shapes[0].cols
            return (4/3) * n**3 if m >= n else (4/3) * m**3  # Approximate QR complexity
        
        # Default complexity for unknown operations
        return self.output_shape.size
    
    def validate_shapes(self) -> bool:
        """Validate that input and output shapes are compatible"""
        if self.operation_type == MOOperationType.MATRIX_MULTIPLY:
            if len(self.input_shapes) >= 2:
                return self.input_shapes[0].cols == self.input_shapes[1].rows
        elif self.operation_type in [MOOperationType.MATRIX_ADD, MOOperationType.MATRIX_SUBTRACT]:
            if len(self.input_shapes) >= 2:
                return (self.input_shapes[0].rows == self.input_shapes[1].rows and
                       self.input_shapes[0].cols == self.input_shapes[1].cols)
        elif self.operation_type == MOOperationType.TRANSPOSE:
            if len(self.input_shapes) >= 1:
                return (self.input_shapes[0].rows == self.output_shape.cols and
                       self.input_shapes[0].cols == self.output_shape.rows)
        
        return True  # Default to valid for unknown operations
    
    def __str__(self):
        return f"MODFGNode({self.node_id}, {self.operation_type.value}, {self.output_shape})"


class MatrixOperationDFG:
    """
    Matrix Operation Data Flow Graph
    
    Represents the complete data flow graph for matrix operations
    corresponding to a single factor in the factor graph.
    """
    
    def __init__(self, factor_id: str):
        """
        Initialize MO-DFG for a specific factor
        
        Args:
            factor_id: ID of the factor this MO-DFG represents
        """
        self.factor_id = factor_id
        self.nodes: Dict[str, MODFGNode] = {}
        self.graph = nx.DiGraph()  # Directed graph for data flow
        
        # Input/Output nodes
        self.input_nodes: List[str] = []   # Nodes that load input data
        self.output_nodes: List[str] = []  # Nodes that produce final outputs
        
        # Execution information
        self.execution_order: List[str] = []  # Topologically sorted execution order
        self.is_compiled: bool = False
        
    def add_node(self, node: MODFGNode):
        """Add a node to the MO-DFG"""
        self.nodes[node.node_id] = node
        self.graph.add_node(node.node_id, node_obj=node)
    
    def add_edge(self, from_node_id: str, to_node_id: str):
        """Add a data flow edge between two nodes"""
        if from_node_id in self.nodes and to_node_id in self.nodes:
            self.graph.add_edge(from_node_id, to_node_id)
            self.nodes[from_node_id].add_output_node(self.nodes[to_node_id])
    
    def remove_node(self, node_id: str):
        """Remove a node from the MO-DFG"""
        if node_id in self.nodes:
            node = self.nodes[node_id]
            # Remove all connections
            for input_node in node.input_nodes[:]:
                node.remove_input_node(input_node)
            for output_node in node.output_nodes[:]:
                node.remove_output_node(output_node)
            # Remove from graph
            del self.nodes[node_id]
            self.graph.remove_node(node_id)
    
    def get_topological_order(self) -> List[str]:
        """Get topological ordering of nodes for execution"""
        try:
            return list(nx.topological_sort(self.graph))
        except nx.NetworkXError:
            raise ValueError("MO-DFG contains cycles, cannot determine execution order")
    
    def validate(self) -> bool:
        """Validate the MO-DFG structure"""
        # Check for cycles
        if not nx.is_directed_acyclic_graph(self.graph):
            return False
        
        # Validate each node's shape compatibility
        for node in self.nodes.values():
            if not node.validate_shapes():
                return False
        
        return True
    
    def get_total_complexity(self) -> int:
        """Get total computational complexity of the MO-DFG"""
        return sum(node.get_computational_complexity() for node in self.nodes.values())
    
    def mark_input_nodes(self, node_ids: List[str]):
        """Mark nodes as input nodes (data loading)"""
        self.input_nodes = [nid for nid in node_ids if nid in self.nodes]
    
    def mark_output_nodes(self, node_ids: List[str]):
        """Mark nodes as output nodes (final results)"""
        self.output_nodes = [nid for nid in node_ids if nid in self.nodes]
    
    def compile_execution_order(self):
        """Compile the execution order and mark as compiled"""
        self.execution_order = self.get_topological_order()
        for i, node_id in enumerate(self.execution_order):
            self.nodes[node_id].execution_order = i
        self.is_compiled = True
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about the MO-DFG"""
        op_counts = {}
        for node in self.nodes.values():
            op_type = node.operation_type.value
            op_counts[op_type] = op_counts.get(op_type, 0) + 1
        
        return {
            'factor_id': self.factor_id,
            'num_nodes': len(self.nodes),
            'num_edges': self.graph.number_of_edges(),
            'total_complexity': self.get_total_complexity(),
            'operation_counts': op_counts,
            'is_compiled': self.is_compiled,
            'has_cycles': not nx.is_directed_acyclic_graph(self.graph)
        }
    
    def __str__(self):
        stats = self.get_statistics()
        return f"MatrixOperationDFG(factor={self.factor_id}, nodes={stats['num_nodes']}, edges={stats['num_edges']})"
